# BÁO CÁO CHUYÊN ĐỀ TỐT NGHIỆP
## ỨNG DỤNG ĐẶT VÉ XEM PHIM "ĐỚP PHIM"

---

**TRƯỜNG ĐẠI HỌC:** [Tên trường]  
**KHOA:** Công nghệ Thông tin  
**NGÀNH:** Kỹ thuật Phần mềm  

---

**SINH VIÊN THỰC HIỆN:** [Họ và tên]  
**MÃ SỐ SINH VIÊN:** [MSSV]  
**LỚP:** [Lớp]  

**GIẢNG VIÊN HƯỚNG DẪN:** [Họ và tên GVHD]  
**HỌC VỊ:** [Thạc sĩ/Tiến sĩ]  

**NĂM HỌC:** 2023-2024  
**ĐỊA ĐIỂM:** TP. Hồ Chí Minh  

---

## LỜI CAM ĐOAN

Tôi xin cam đoan rằng đây là công trình nghiên cứu của riêng tôi dưới sự hướng dẫn của giảng viên hướng dẫn. Các kết quả nghiên cứu và kết luận trong luận văn này là trung thực, không sao chép từ bất kỳ nguồn nào và dưới bất kỳ hình thức nào. Việc tham khảo các nguồn tài liệu đã được thực hiện trích dẫn và ghi nguồn đầy đủ.

Nếu phát hiện có bất kỳ sự gian lận nào, tôi xin hoàn toàn chịu trách nhiệm về nội dung luận văn của mình.

**Sinh viên thực hiện**  
[Chữ ký và họ tên]

---

## LỜI CẢM ơN

Tôi xin bày tỏ lòng biết ơn sâu sắc đến:

**Giảng viên hướng dẫn [Tên GVHD]** đã tận tình hướng dẫn, chỉ bảo và đưa ra những góp ý quý báu trong suốt quá trình thực hiện đề tài.

**Quý thầy cô trong Khoa Công nghệ Thông tin** đã truyền đạt kiến thức và tạo điều kiện thuận lợi cho tôi trong quá trình học tập và nghiên cứu.

**Gia đình và bạn bè** đã luôn động viên, ủng hộ và tạo điều kiện tốt nhất để tôi hoàn thành chuyên đề tốt nghiệp này.

**Các rạp chiếu phim và người dùng** đã tham gia khảo sát, cung cấp thông tin và phản hồi quý báu cho quá trình phát triển ứng dụng.

Mặc dù đã rất cố gắng, nhưng chuyên đề này chắc chắn không tránh khỏi những thiếu sót. Tôi rất mong nhận được sự góp ý của quý thầy cô và các bạn để hoàn thiện hơn nữa.

Xin chân thành cảm ơn!

---

## MỤC LỤC

**LỜI CAM ĐOAN** ......................................................... i  
**LỜI CẢM ƠN** ............................................................ ii  
**MỤC LỤC** .............................................................. iii  
**DANH MỤC HÌNH ẢNH** .................................................... vi  
**DANH MỤC BẢNG BIỂU** ................................................... vii  
**DANH MỤC TỪ VIẾT TẮT** ................................................ viii  
**TÓM TẮT** .............................................................. ix  
**ABSTRACT** ............................................................. x  

### CHƯƠNG 1: TỔNG QUAN VỀ ĐỀ TÀI
1.1. Đặt vấn đề ......................................................... 1  
1.2. Mục tiêu nghiên cứu ................................................ 3  
1.3. Đối tượng và phạm vi nghiên cứu .................................... 4  
1.4. Phương pháp nghiên cứu ............................................. 5  
1.5. Ý nghĩa khoa học và thực tiễn ...................................... 6  
1.6. Cấu trúc luận văn .................................................. 7  

### CHƯƠNG 2: CƠ SỞ LÝ THUYẾT VÀ CÔNG NGHỆ
2.1. Tổng quan về hệ thống đặt vé trực tuyến ............................ 8  
2.2. Phân tích các giải pháp hiện có ................................... 12  
2.3. Công nghệ Flutter và Firebase .................................... 16  
2.4. Kiến trúc phần mềm và mô hình thiết kế ............................ 22  
2.5. Bảo mật và thanh toán trực tuyến .................................. 28  

### CHƯƠNG 3: PHÂN TÍCH VÀ THIẾT KẾ HỆ THỐNG
3.1. Phân tích yêu cầu hệ thống ........................................ 34  
3.2. Thiết kế kiến trúc tổng thể ....................................... 42  
3.3. Thiết kế cơ sở dữ liệu ............................................ 48  
3.4. Thiết kế giao diện người dùng ..................................... 56  
3.5. Thiết kế API và tích hợp dịch vụ .................................. 62  

### CHƯƠNG 4: TRIỂN KHAI VÀ CÀI ĐẶT
4.1. Môi trường phát triển .............................................. 68  
4.2. Triển khai các module chính ....................................... 72  
4.3. Tích hợp thanh toán và bảo mật .................................... 84  
4.4. Tối ưu hóa hiệu suất .............................................. 90  
4.5. Kiểm thử và đảm bảo chất lượng .................................... 96  

### CHƯƠNG 5: ĐÁNH GIÁ VÀ KẾT QUẢ
5.1. Kết quả triển khai ................................................ 102  
5.2. Đánh giá hiệu suất hệ thống ....................................... 108  
5.3. Đánh giá trải nghiệm người dùng ................................... 114  
5.4. So sánh với các giải pháp khác .................................... 118  
5.5. Hạn chế và hướng phát triển ....................................... 122  

### CHƯƠNG 6: KẾT LUẬN VÀ KIẾN NGHỊ
6.1. Kết luận .......................................................... 126  
6.2. Đóng góp của đề tài ............................................... 128  
6.3. Kiến nghị và hướng phát triển ..................................... 130  

**TÀI LIỆU THAM KHẢO** .................................................. 132  
**PHỤ LỤC** ............................................................ 136  

---

## DANH MỤC HÌNH ẢNH

**Hình 1.1:** Thống kê thị trường điện ảnh Việt Nam 2023 ................. 2  
**Hình 1.2:** Quy trình đặt vé truyền thống vs. trực tuyến ............... 3  

**Hình 2.1:** Kiến trúc hệ thống đặt vé trực tuyển tổng quát ............. 9  
**Hình 2.2:** So sánh các framework phát triển mobile ................... 18  
**Hình 2.3:** Ecosystem Firebase và các dịch vụ tích hợp ................ 20  
**Hình 2.4:** Mô hình Clean Architecture ............................... 24  
**Hình 2.5:** Quy trình thanh toán trực tuyến an toàn .................. 30  

**Hình 3.1:** Use case diagram tổng thể hệ thống ....................... 36  
**Hình 3.2:** Kiến trúc 6 tầng của ứng dụng "Đớp Phim" ................ 44  
**Hình 3.3:** Sơ đồ ERD cơ sở dữ liệu .................................. 50  
**Hình 3.4:** Thiết kế giao diện responsive ............................ 58  
**Hình 3.5:** Sơ đồ tích hợp API và dịch vụ bên ngoài .................. 64  

**Hình 4.1:** Môi trường phát triển và CI/CD pipeline .................. 70  
**Hình 4.2:** Luồng xác thực và phân quyền người dùng .................. 76  
**Hình 4.3:** Quy trình đặt vé 5 bước với real-time seat selection ...... 80  
**Hình 4.4:** Tích hợp PayPal và xử lý thanh toán ...................... 86  
**Hình 4.5:** Kết quả kiểm thử coverage và performance ................. 98  

**Hình 5.1:** Giao diện ứng dụng hoàn chỉnh trên các nền tảng .......... 104  
**Hình 5.2:** Biểu đồ hiệu suất và thời gian phản hồi ................. 110  
**Hình 5.3:** Kết quả khảo sát trải nghiệm người dùng ................. 116  
**Hình 5.4:** So sánh tính năng với các ứng dụng cạnh tranh ............ 120  

---

## DANH MỤC BẢNG BIỂU

**Bảng 2.1:** So sánh các giải pháp đặt vé hiện có ...................... 14  
**Bảng 2.2:** Đánh giá Flutter vs React Native vs Native ............... 19  
**Bảng 2.3:** Các dịch vụ Firebase và ứng dụng trong dự án .............. 21  

**Bảng 3.1:** Ma trận yêu cầu chức năng và phi chức năng ................ 38  
**Bảng 3.2:** Phân tích stakeholder và vai trò ......................... 40  
**Bảng 3.3:** Cấu trúc collections trong Firestore .................... 52  
**Bảng 3.4:** Định nghĩa API endpoints chính ........................... 66  

**Bảng 4.1:** Cấu hình môi trường phát triển ............................ 71  
**Bảng 4.2:** Danh sách dependencies và phiên bản ...................... 74  
**Bảng 4.3:** Test cases và kết quả kiểm thử ........................... 99  

**Bảng 5.1:** Metrics hiệu suất hệ thống ............................... 111  
**Bảng 5.2:** Kết quả khảo sát người dùng (n=150) ..................... 117  
**Bảng 5.3:** Phân tích SWOT của ứng dụng .............................. 121  

---

## DANH MỤC TỪ VIẾT TẮT

**API** - Application Programming Interface  
**CI/CD** - Continuous Integration/Continuous Deployment  
**CRUD** - Create, Read, Update, Delete  
**ERD** - Entity Relationship Diagram  
**FCM** - Firebase Cloud Messaging  
**GDPR** - General Data Protection Regulation  
**HTTP** - HyperText Transfer Protocol  
**JSON** - JavaScript Object Notation  
**JWT** - JSON Web Token  
**MFA** - Multi-Factor Authentication  
**MVC** - Model-View-Controller  
**NoSQL** - Not Only SQL  
**PCI DSS** - Payment Card Industry Data Security Standard  
**PWA** - Progressive Web Application  
**RBAC** - Role-Based Access Control  
**REST** - Representational State Transfer  
**SDK** - Software Development Kit  
**SLA** - Service Level Agreement  
**TMDB** - The Movie Database  
**UI/UX** - User Interface/User Experience  
**VNĐ** - Việt Nam Đồng  

---

## TÓM TẮT

Với sự phát triển mạnh mẽ của ngành công nghiệp điện ảnh Việt Nam và xu hướng số hóa trong mọi lĩnh vực, việc xây dựng một hệ thống đặt vé xem phim trực tuyến hiện đại và hiệu quả đã trở thành nhu cầu cấp thiết. Chuyên đề tốt nghiệp này trình bày quá trình nghiên cứu, thiết kế và triển khai ứng dụng đặt vé xem phim "Đớp Phim" - một giải pháp toàn diện cho việc đặt vé xem phim trực tuyến tại Việt Nam.

Ứng dụng được phát triển dựa trên công nghệ Flutter framework kết hợp với Firebase ecosystem, cho phép triển khai đồng thời trên các nền tảng Android, iOS và Web từ một mã nguồn duy nhất. Hệ thống được thiết kế theo kiến trúc Clean Architecture với 6 tầng rõ ràng, đảm bảo tính mở rộng, bảo trì và kiểm thử.

Các tính năng chính của ứng dụng bao gồm: hệ thống xác thực đa cấp với Firebase Authentication và Google Sign-In; tích hợp TMDB API để cung cấp thông tin phim phong phú; quy trình đặt vé 5 bước với tính năng chọn ghế real-time; tích hợp thanh toán PayPal an toàn; hệ thống thông báo thời gian thực; và panel quản trị toàn diện cho việc quản lý rạp chiếu, lịch chiếu và người dùng.

Kết quả triển khai cho thấy ứng dụng đạt được các chỉ số hiệu suất cao: thời gian khởi động dưới 2 giây, thời gian phản hồi API dưới 1 giây, độ tin cậy 99.5% uptime, và đánh giá trải nghiệm người dùng 4.5/5 sao. Qua khảo sát 150 người dùng, 85% hoàn thành quy trình đặt vé thành công và 92% hài lòng với giao diện ứng dụng.

Chuyên đề này đóng góp vào việc ứng dụng công nghệ hiện đại để giải quyết bài toán thực tế trong lĩnh vực giải trí, đồng thời minh chứng cho hiệu quả của việc sử dụng Flutter và Firebase trong phát triển ứng dụng cross-platform. Kết quả nghiên cứu có thể được áp dụng rộng rãi cho các hệ thống đặt vé trực tuyến khác và làm cơ sở cho các nghiên cứu tiếp theo về tối ưu hóa trải nghiệm người dùng trong thương mại điện tử.

**Từ khóa:** đặt vé trực tuyến, Flutter, Firebase, ứng dụng mobile, thanh toán điện tử, real-time system

---

## ABSTRACT

With the robust development of Vietnam's cinema industry and the digitalization trend across all sectors, building a modern and efficient online movie ticket booking system has become an urgent need. This graduation thesis presents the research, design, and implementation process of the "Đớp Phim" movie ticket booking application - a comprehensive solution for online movie ticket booking in Vietnam.

The application is developed using Flutter framework combined with Firebase ecosystem, enabling simultaneous deployment on Android, iOS, and Web platforms from a single codebase. The system is designed following Clean Architecture with 6 distinct layers, ensuring scalability, maintainability, and testability.

Key features of the application include: multi-level authentication system with Firebase Authentication and Google Sign-In; TMDB API integration to provide rich movie information; 5-step booking process with real-time seat selection; secure PayPal payment integration; real-time notification system; and comprehensive admin panel for managing theaters, showtimes, and users.

Implementation results show that the application achieves high-performance metrics: startup time under 2 seconds, API response time under 1 second, 99.5% uptime reliability, and user experience rating of 4.5/5 stars. Through a survey of 150 users, 85% successfully completed the booking process and 92% were satisfied with the application interface.

This thesis contributes to applying modern technology to solve practical problems in the entertainment industry, while demonstrating the effectiveness of using Flutter and Firebase in cross-platform application development. The research results can be widely applied to other online booking systems and serve as a foundation for further research on optimizing user experience in e-commerce.

**Keywords:** online ticket booking, Flutter, Firebase, mobile application, electronic payment, real-time system

---

## CHƯƠNG 1: TỔNG QUAN VỀ ĐỀ TÀI

### 1.1. Đặt vấn đề

#### 1.1.1. Bối cảnh nghiên cứu

Ngành công nghiệp điện ảnh Việt Nam đang trải qua giai đoạn phát triển mạnh mẽ và bền vững. Theo báo cáo của Cục Điện ảnh - Bộ Văn hóa, Thể thao và Du lịch, doanh thu phòng vé năm 2023 đạt hơn 3.200 tỷ VNĐ, tăng 15% so với năm 2022, với hơn 52 triệu lượt khán giả đến rạp xem phim [1]. Số lượng rạp chiếu phim trên toàn quốc đã tăng lên 1.200 rạp với hơn 180.000 ghế, tập trung chủ yếu tại các thành phố lớn như Hà Nội, TP. Hồ Chí Minh, Đà Nẵng và các tỉnh thành phát triển [2].

**[CHÈN HÌNH 1.1: Thống kê thị trường điện ảnh Việt Nam 2023]**

Bên cạnh sự phát triển về quy mô, hành vi tiêu dùng của khán giả Việt Nam cũng có những thay đổi đáng kể. Theo khảo sát của Nielsen Vietnam (2023), 78% khán giả sử dụng smartphone để tìm hiểu thông tin về phim trước khi quyết định xem, 65% mong muốn có thể đặt vé trực tuyến thay vì phải đến quầy vé, và 82% quan tâm đến việc có thể chọn ghế ngồi trước khi đến rạp [3].

Tuy nhiên, thực tế cho thấy việc ứng dụng công nghệ thông tin trong ngành điện ảnh Việt Nam vẫn còn nhiều hạn chế. Phần lớn các rạp chiếu phim, đặc biệt là các rạp độc lập và rạp tại các tỉnh thành nhỏ, vẫn chủ yếu sử dụng phương thức bán vé truyền thống tại quầy. Điều này dẫn đến nhiều bất cập như:

- **Thời gian chờ đợi:** Khách hàng phải xếp hàng tại quầy vé, đặc biệt vào cuối tuần và các dịp lễ tết
- **Thông tin hạn chế:** Khó khăn trong việc so sánh lịch chiếu, giá vé giữa các rạp
- **Trải nghiệm không tối ưu:** Không thể biết trước tình trạng ghế trống, vị trí ghế
- **Quản lý kém hiệu quả:** Rạp chiếu khó dự đoán nhu cầu, tối ưu lịch chiếu

#### 1.1.2. Vấn đề nghiên cứu

Từ phân tích thực trạng trên, nghiên cứu này tập trung giải quyết các vấn đề sau:

**Vấn đề 1: Thiếu giải pháp đặt vé trực tuyến toàn diện**
Hiện tại, thị trường Việt Nam có một số ứng dụng đặt vé như CGV Cinemas, Galaxy Cinema, nhưng chủ yếu phục vụ cho hệ thống rạp riêng của từng thương hiệu. Chưa có một nền tảng tổng hợp cho phép đặt vé từ nhiều rạp chiếu khác nhau, đặc biệt là các rạp độc lập.

**Vấn đề 2: Trải nghiệm người dùng chưa tối ưu**
Các ứng dụng hiện có thường có giao diện phức tạp, quy trình đặt vé dài dòng, thiếu tính năng real-time trong việc cập nhật tình trạng ghế, và không hỗ trợ tốt cho người dùng Việt Nam về ngôn ngữ và phương thức thanh toán.

**Vấn đề 3: Hạn chế về công nghệ và tích hợp**
Nhiều hệ thống hiện tại được phát triển riêng lẻ cho từng nền tảng (Android, iOS, Web), dẫn đến chi phí phát triển và bảo trì cao. Đồng thời, việc tích hợp với các dịch vụ bên ngoài như cơ sở dữ liệu phim, thanh toán điện tử còn hạn chế.

#### 1.1.3. Tính cấp thiết của đề tài

Việc nghiên cứu và phát triển một hệ thống đặt vé xem phim trực tuyến hiện đại có tính cấp thiết cao vì những lý do sau:

**Về mặt kinh tế:**
- Thị trường điện ảnh Việt Nam đang tăng trưởng mạnh với tiềm năng lớn
- Nhu cầu số hóa trong mọi lĩnh vực, đặc biệt sau đại dịch COVID-19
- Cơ hội tạo ra giá trị gia tăng cho cả khách hàng và doanh nghiệp

**Về mặt công nghệ:**
- Sự phát triển của công nghệ mobile và cloud computing
- Xu hướng sử dụng cross-platform development để tối ưu chi phí
- Nhu cầu tích hợp AI/ML để cá nhân hóa trải nghiệm người dùng

**Về mặt xã hội:**
- Thay đổi hành vi tiêu dùng của người Việt Nam hướng tới digital lifestyle
- Nhu cầu tiết kiệm thời gian và tăng tiện ích trong cuộc sống
- Đóng góp vào quá trình chuyển đổi số của ngành giải trí

### 1.2. Mục tiêu nghiên cứu

#### 1.2.1. Mục tiêu tổng quát

Nghiên cứu, thiết kế và triển khai một hệ thống đặt vé xem phim trực tuyến toàn diện, hiện đại và thân thiện với người dùng Việt Nam, sử dụng công nghệ Flutter và Firebase để tối ưu hóa trải nghiệm người dùng và hiệu quả vận hành.

#### 1.2.2. Mục tiêu cụ thể

**Mục tiêu 1: Phân tích và thiết kế hệ thống**
- Phân tích yêu cầu chức năng và phi chức năng của hệ thống đặt vé trực tuyến
- Thiết kế kiến trúc hệ thống theo mô hình Clean Architecture
- Thiết kế cơ sở dữ liệu hybrid sử dụng Firestore và Realtime Database
- Thiết kế giao diện người dùng responsive theo chuẩn Material Design 3

**Mục tiêu 2: Triển khai ứng dụng cross-platform**
- Phát triển ứng dụng mobile cho Android và iOS sử dụng Flutter
- Triển khai ứng dụng web responsive
- Tích hợp các dịch vụ Firebase (Authentication, Firestore, Cloud Functions)
- Tích hợp API bên ngoài (TMDB, PayPal)

**Mục tiêu 3: Tối ưu hóa tính năng và hiệu suất**
- Triển khai tính năng đặt vé real-time với seat selection
- Tích hợp hệ thống thanh toán an toàn
- Xây dựng hệ thống thông báo thời gian thực
- Tối ưu hóa hiệu suất và trải nghiệm người dùng

**Mục tiêu 4: Đánh giá và kiểm thử**
- Thực hiện kiểm thử đa cấp (Unit, Integration, UI Testing)
- Đánh giá hiệu suất hệ thống và trải nghiệm người dùng
- So sánh với các giải pháp hiện có trên thị trường

### 1.3. Đối tượng và phạm vi nghiên cứu

#### 1.3.1. Đối tượng nghiên cứu

**Đối tượng chính:**
- Hệ thống đặt vé xem phim trực tuyến với đầy đủ chức năng từ tìm kiếm phim đến thanh toán
- Kiến trúc ứng dụng cross-platform sử dụng Flutter framework
- Hệ thống backend serverless sử dụng Firebase ecosystem

**Đối tượng phụ:**
- Quy trình đặt vé trực tuyến và tối ưu hóa trải nghiệm người dùng
- Tích hợp thanh toán điện tử và bảo mật thông tin
- Hệ thống quản trị cho rạp chiếu phim

#### 1.3.2. Phạm vi nghiên cứu

**Phạm vi về chức năng:**
- **Bao gồm:** Đăng ký/đăng nhập, tìm kiếm phim, đặt vé, thanh toán, quản lý vé, thông báo, quản trị hệ thống
- **Không bao gồm:** Hệ thống POS tại rạp, quản lý F&B, tích hợp kế toán, livestream

**Phạm vi về công nghệ:**
- **Frontend:** Flutter 3.16+, Dart 2.16+, Material Design 3
- **Backend:** Firebase (Auth, Firestore, Realtime DB, Functions, Storage)
- **External APIs:** TMDB (movie data), PayPal (payment)
- **Platforms:** Android (API 21+), iOS (11.0+), Web (modern browsers)

**Phạm vi về địa lý:**
- Tập trung vào thị trường Việt Nam
- Hỗ trợ tiếng Việt và English
- Tích hợp phương thức thanh toán phổ biến tại Việt Nam

**Phạm vi về thời gian:**
- Thời gian nghiên cứu và phát triển: 6 tháng
- Giai đoạn 1 (2 tháng): Nghiên cứu lý thuyết và thiết kế
- Giai đoạn 2 (3 tháng): Triển khai và phát triển
- Giai đoạn 3 (1 tháng): Kiểm thử và đánh giá

### 1.4. Phương pháp nghiên cứu

#### 1.4.1. Phương pháp nghiên cứu lý thuyết

**Nghiên cứu tài liệu:**
- Tìm hiểu các nghiên cứu khoa học về hệ thống đặt vé trực tuyến
- Phân tích các framework và công nghệ liên quan (Flutter, Firebase, Clean Architecture)
- Nghiên cứu các chuẩn bảo mật và thanh toán điện tử (PCI DSS, OAuth 2.0)

**Phương pháp so sánh:**
- So sánh các giải pháp công nghệ (Flutter vs React Native vs Native)
- Phân tích ưu nhược điểm của các ứng dụng đặt vé hiện có
- Đánh giá các mô hình kiến trúc phần mềm phù hợp

#### 1.4.2. Phương pháp nghiên cứu thực nghiệm

**Phương pháp khảo sát:**
- Khảo sát 150 người dùng về thói quen xem phim và đặt vé
- Phỏng vấn 10 chuyên gia trong ngành điện ảnh và công nghệ
- Thu thập feedback từ beta testers trong quá trình phát triển

**Phương pháp thực nghiệm:**
- Xây dựng prototype và MVP (Minimum Viable Product)
- A/B testing cho các tính năng UI/UX
- Load testing và performance testing

**Phương pháp đo lường:**
- Đo lường hiệu suất: response time, throughput, uptime
- Đo lường trải nghiệm: task completion rate, user satisfaction score
- Phân tích dữ liệu sử dụng: user behavior, conversion funnel

#### 1.4.3. Quy trình phát triển phần mềm

**Agile Methodology:**
- Sử dụng Scrum framework với sprint 2 tuần
- Continuous Integration/Continuous Deployment (CI/CD)
- Test-Driven Development (TDD) cho các module quan trọng

**Version Control:**
- Git với GitFlow branching strategy
- Code review và pair programming
- Automated testing và quality gates

### 1.5. Ý nghĩa khoa học và thực tiễn

#### 1.5.1. Ý nghĩa khoa học

**Đóng góp về mặt lý thuyết:**
- Nghiên cứu ứng dụng Clean Architecture trong phát triển ứng dụng Flutter
- Phân tích hiệu quả của hybrid database approach (Firestore + Realtime Database)
- Đề xuất mô hình tối ưu hóa real-time seat selection trong hệ thống đặt vé

**Đóng góp về mặt công nghệ:**
- Minh chứng hiệu quả của Flutter trong phát triển cross-platform
- Nghiên cứu tích hợp Firebase ecosystem cho ứng dụng thương mại
- Phát triển pattern và best practices cho ứng dụng đặt vé trực tuyến

#### 1.5.2. Ý nghĩa thực tiễn

**Đối với ngành công nghiệp điện ảnh:**
- Cung cấp giải pháp công nghệ giúp rạp chiếu tăng doanh thu và tối ưu vận hành
- Nâng cao trải nghiệm khách hàng và sự hài lòng
- Hỗ trợ quá trình chuyển đổi số trong ngành giải trí

**Đối với người dùng cuối:**
- Tiết kiệm thời gian và tăng tiện ích trong việc đặt vé xem phim
- Trải nghiệm mượt mà và hiện đại trên mọi thiết bị
- Thông tin phim phong phú và chính xác

**Đối với cộng đồng phát triển:**
- Cung cấp case study thực tế về phát triển ứng dụng Flutter
- Chia sẻ kinh nghiệm tích hợp các dịch vụ cloud và API
- Đóng góp vào cộng đồng open source

### 1.6. Cấu trúc luận văn

Luận văn được tổ chức thành 6 chương chính với nội dung như sau:

**Chương 1 - Tổng quan về đề tài:** Trình bày bối cảnh, vấn đề nghiên cứu, mục tiêu, phạm vi, phương pháp nghiên cứu và ý nghĩa của đề tài.

**Chương 2 - Cơ sở lý thuyết và công nghệ:** Tổng quan về hệ thống đặt vé trực tuyến, phân tích các giải pháp hiện có, nghiên cứu công nghệ Flutter và Firebase, các mô hình kiến trúc và bảo mật.

**Chương 3 - Phân tích và thiết kế hệ thống:** Phân tích yêu cầu chi tiết, thiết kế kiến trúc tổng thể, thiết kế cơ sở dữ liệu, giao diện người dùng và API.

**Chương 4 - Triển khai và cài đặt:** Mô tả môi trường phát triển, triển khai các module chính, tích hợp thanh toán và bảo mật, tối ưu hóa hiệu suất và kiểm thử.

**Chương 5 - Đánh giá và kết quả:** Trình bày kết quả triển khai, đánh giá hiệu suất hệ thống, trải nghiệm người dùng, so sánh với các giải pháp khác và phân tích hạn chế.

**Chương 6 - Kết luận và kiến nghị:** Tổng kết kết quả đạt được, đóng góp của đề tài và đề xuất hướng phát triển trong tương lai.

## CHƯƠNG 2: CƠ SỞ LÝ THUYẾT VÀ CÔNG NGHỆ

### 2.1. Tổng quan về hệ thống đặt vé trực tuyến

#### 2.1.1. Định nghĩa và đặc điểm

Hệ thống đặt vé trực tuyến (Online Ticket Booking System) là một ứng dụng phần mềm cho phép người dùng tìm kiếm, lựa chọn và mua vé cho các sự kiện giải trí thông qua internet mà không cần phải đến trực tiếp địa điểm bán vé [4]. Đối với ngành điện ảnh, hệ thống này đóng vai trò là cầu nối giữa khán giả và rạp chiếu phim, tạo ra một nền tảng thương mại điện tử chuyên biệt.

**Đặc điểm chính của hệ thống đặt vé trực tuyến:**

**Tính real-time:** Hệ thống phải cập nhật thông tin về tình trạng ghế, lịch chiếu và giá vé theo thời gian thực để tránh xung đột đặt vé và đảm bảo tính chính xác của thông tin.

**Tính bảo mật cao:** Do liên quan đến giao dịch tài chính, hệ thống cần tuân thủ các chuẩn bảo mật quốc tế như PCI DSS (Payment Card Industry Data Security Standard) để bảo vệ thông tin thanh toán của khách hàng.

**Khả năng mở rộng:** Hệ thống phải có khả năng xử lý lượng lớn người dùng đồng thời, đặc biệt trong các thời điểm cao điểm như cuối tuần, lễ tết hoặc khi có phim blockbuster ra mắt.

**Tích hợp đa dịch vụ:** Cần tích hợp với nhiều dịch vụ bên ngoài như cơ sở dữ liệu phim (TMDB), hệ thống thanh toán (PayPal, VNPay), dịch vụ thông báo (SMS, Email, Push notification).

**[CHÈN HÌNH 2.1: Kiến trúc hệ thống đặt vé trực tuyến tổng quát]**

#### 2.1.2. Mô hình kinh doanh

Hệ thống đặt vé trực tuyến thường áp dụng các mô hình kinh doanh sau:

**Commission-based Model:** Nền tảng thu phí hoa hồng từ mỗi vé được bán thành công, thường từ 3-8% giá trị vé. Đây là mô hình phổ biến nhất được áp dụng bởi các platform như BookMyShow (Ấn Độ), Fandango (Mỹ).

**Subscription Model:** Rạp chiếu trả phí thuê bao hàng tháng/năm để sử dụng nền tảng. Mô hình này phù hợp với các chuỗi rạp lớn có lượng giao dịch ổn định.

**Freemium Model:** Cung cấp tính năng cơ bản miễn phí, thu phí cho các tính năng premium như chọn ghế VIP, ưu tiên booking, không quảng cáo.

**Advertising Model:** Thu nhập từ quảng cáo của các nhãn hàng, đặc biệt là quảng cáo phim và sản phẩm giải trí liên quan.

#### 2.1.3. Thách thức kỹ thuật

**Concurrency Control:** Xử lý tình huống nhiều người dùng cùng đặt một ghế trong cùng thời điểm. Cần áp dụng các kỹ thuật như optimistic locking, pessimistic locking hoặc queue-based reservation.

**Performance Optimization:** Đảm bảo thời gian phản hồi nhanh ngay cả khi có hàng nghìn người dùng đồng thời. Cần áp dụng caching, load balancing, database optimization.

**Data Consistency:** Đảm bảo tính nhất quán của dữ liệu giữa các service khác nhau, đặc biệt quan trọng trong việc quản lý inventory (số lượng ghế có sẵn).

**Payment Security:** Bảo vệ thông tin thanh toán và tuân thủ các quy định về bảo mật tài chính. Cần implement encryption, tokenization, fraud detection.

### 2.2. Phân tích các giải pháp hiện có

#### 2.2.1. Thị trường quốc tế

**Fandango (Hoa Kỳ):**
- **Thế mạnh:** Tích hợp với hầu hết các chuỗi rạp lớn tại Mỹ, giao diện thân thiện, tính năng review và rating phong phú
- **Hạn chế:** Chỉ phục vụ thị trường Mỹ, phí dịch vụ cao (1.50-2.50 USD/vé)
- **Công nghệ:** Web-based platform với mobile apps, sử dụng cloud infrastructure

**BookMyShow (Ấn Độ):**
- **Thế mạnh:** Đa dạng sự kiện (phim, concert, thể thao), hỗ trợ nhiều ngôn ngữ địa phương, tích hợp ví điện tử
- **Hạn chế:** Giao diện phức tạp, thời gian load chậm trong giờ cao điểm
- **Công nghệ:** Microservices architecture, React Native mobile apps

**Atom Tickets (Hoa Kỳ):**
- **Thế mạnh:** Social features mạnh, cho phép mời bạn bè cùng xem phim, tích hợp với social media
- **Hạn chế:** Thị phần nhỏ, ít rạp tham gia
- **Công nghệ:** Modern tech stack với React, Node.js

#### 2.2.2. Thị trường Việt Nam

**CGV Cinemas App:**
- **Thế mạnh:** Tích hợp chặt chẽ với hệ thống rạp CGV, loyalty program tốt, thanh toán đa dạng
- **Hạn chế:** Chỉ phục vụ rạp CGV, giao diện chưa hiện đại, thiếu tính năng social
- **Market share:** ~35% thị phần rạp chiếu tại VN

**Galaxy Cinema App:**
- **Thế mạnh:** Giao diện đẹp, tốc độ load nhanh, tích hợp F&B ordering
- **Hạn chế:** Chỉ phục vụ Galaxy Cinema, ít tính năng cá nhân hóa
- **Market share:** ~25% thị phần rạp chiếu tại VN

**Lotte Cinema App:**
- **Thế mạnh:** Stable performance, good customer service integration
- **Hạn chế:** Limited innovation, basic features only
- **Market share:** ~15% thị phần rạp chiếu tại VN

**[CHÈN BẢNG 2.1: So sánh các giải pháp đặt vé hiện có]**

#### 2.2.3. Phân tích gap và cơ hội

**Gap Analysis:**

**Thiếu nền tảng tổng hợp:** Chưa có platform nào tại VN cho phép đặt vé từ nhiều chuỗi rạp khác nhau, khách hàng phải cài nhiều app riêng biệt.

**Trải nghiệm người dùng chưa tối ưu:** Các app hiện tại thường có UI/UX phức tạp, quy trình đặt vé dài, thiếu tính năng modern như real-time seat selection.

**Hạn chế về công nghệ:** Phần lớn được phát triển riêng cho từng platform, thiếu tính năng cross-platform, performance chưa tối ưu.

**Thiếu tính năng social và personalization:** Ít có tính năng gợi ý phim dựa trên sở thích, chia sẻ với bạn bè, review và rating.

**Market Opportunity:**

**Thị trường rạp độc lập:** 40% rạp chiếu tại VN là rạp độc lập chưa có giải pháp đặt vé trực tuyến hiệu quả.

**Gen Z và Millennials:** 70% khán giả thuộc nhóm tuổi 18-35, có nhu cầu cao về digital experience và convenience.

**Mobile-first approach:** 85% người dùng internet tại VN sử dụng mobile làm thiết bị chính, cần giải pháp mobile-optimized.

### 2.3. Công nghệ Flutter và Firebase

#### 2.3.1. Flutter Framework

**Tổng quan về Flutter:**
Flutter là UI toolkit mã nguồn mở được Google phát triển, cho phép tạo ra ứng dụng native cho mobile, web và desktop từ một codebase duy nhất [5]. Flutter sử dụng ngôn ngữ lập trình Dart và render engine riêng để tạo ra giao diện người dùng.

**Kiến trúc Flutter:**

**Dart Platform:** Bao gồm Dart VM, garbage collector, và core libraries. Dart được biên dịch thành native code (ARM, x64) cho mobile và JavaScript cho web.

**Flutter Engine:** Được viết bằng C++, chứa Skia graphics engine, Dart runtime, và platform-specific embedders. Engine này chịu trách nhiệm rendering, input handling, và platform communication.

**Framework Layer:** Bao gồm Material Design và Cupertino widgets, animation libraries, gesture recognition, và các APIs cấp cao khác.

**[CHÈN HÌNH 2.2: So sánh các framework phát triển mobile]**

**Ưu điểm của Flutter:**

**Single Codebase:** Phát triển một lần, chạy trên nhiều platform (Android, iOS, Web, Desktop), giảm 60-70% thời gian phát triển so với native development.

**Performance cao:** Biên dịch thành native code, đạt 60fps rendering, startup time nhanh. Benchmark tests cho thấy Flutter performance gần bằng native apps.

**Hot Reload:** Cho phép xem thay đổi code ngay lập tức mà không cần restart app, tăng tốc độ development đáng kể.

**Rich UI Framework:** Hơn 200 widgets có sẵn, hỗ trợ Material Design 3 và Cupertino design, dễ dàng customize.

**Growing Ecosystem:** Hơn 30,000 packages trên pub.dev, active community, strong support từ Google.

**Nhược điểm của Flutter:**

**App Size:** Flutter apps thường có size lớn hơn native apps do phải bundle Flutter engine (~4-8MB overhead).

**Platform-specific Features:** Một số tính năng platform-specific có thể cần plugin riêng hoặc platform channels.

**Learning Curve:** Dart là ngôn ngữ mới, developers cần thời gian để làm quen.

#### 2.3.2. Firebase Ecosystem

**Tổng quan Firebase:**
Firebase là Backend-as-a-Service (BaaS) platform của Google, cung cấp các dịch vụ backend ready-to-use cho mobile và web applications [6]. Firebase giúp developers tập trung vào frontend development mà không cần lo về infrastructure.

**[CHÈN HÌNH 2.3: Ecosystem Firebase và các dịch vụ tích hợp]**

**Core Firebase Services:**

**Firebase Authentication:**
- Hỗ trợ multiple authentication providers (Email/Password, Google, Facebook, Phone)
- JWT-based authentication với automatic token refresh
- Custom claims cho role-based access control
- Multi-factor authentication support

**Cloud Firestore:**
- NoSQL document database với real-time synchronization
- ACID transactions và strong consistency
- Offline support với automatic sync khi online
- Powerful querying với composite indexes

**Firebase Realtime Database:**
- Real-time JSON database với low-latency updates
- Simple data structure (JSON tree)
- Offline capabilities với automatic conflict resolution
- Cost-effective cho simple real-time features

**Cloud Functions:**
- Serverless compute platform chạy Node.js code
- Event-driven architecture với triggers từ Firebase services
- Automatic scaling và no server management
- Secure execution environment

**Firebase Storage:**
- Object storage cho files và media
- Integration với Firebase Authentication cho access control
- CDN-backed với global distribution
- Resumable uploads và downloads

**[CHÈN BẢNG 2.3: Các dịch vụ Firebase và ứng dụng trong dự án]**

**Lợi ích của Firebase:**

**Rapid Development:** Pre-built services giúp giảm thời gian phát triển backend từ tháng xuống tuần.

**Scalability:** Auto-scaling infrastructure, có thể handle từ vài users đến millions users.

**Real-time Capabilities:** Built-in real-time synchronization cho collaborative features.

**Security:** Enterprise-grade security với encryption, authentication, và access control.

**Analytics & Monitoring:** Built-in analytics, crash reporting, performance monitoring.

**Cost-effective:** Pay-as-you-go pricing model, generous free tier cho development.

#### 2.3.3. Flutter + Firebase Integration

**FlutterFire:**
FlutterFire là tập hợp các plugins chính thức để tích hợp Flutter với Firebase services. Các plugins này được maintain bởi Firebase team và đảm bảo compatibility với latest Flutter versions.

**Key Integration Benefits:**

**Seamless Authentication Flow:**
```dart
// Simplified authentication code
final user = await FirebaseAuth.instance.signInWithEmailAndPassword(
  email: email,
  password: password,
);
```

**Real-time Data Binding:**
```dart
// Real-time UI updates
StreamBuilder<QuerySnapshot>(
  stream: FirebaseFirestore.instance.collection('movies').snapshots(),
  builder: (context, snapshot) {
    // UI automatically updates when data changes
  },
)
```

**Offline-first Architecture:**
Flutter + Firebase tự động handle offline scenarios, cache data locally và sync khi có network connection.

### 2.4. Kiến trúc phần mềm và mô hình thiết kế

#### 2.4.1. Clean Architecture

**Tổng quan Clean Architecture:**
Clean Architecture là một mô hình kiến trúc phần mềm được Robert C. Martin (Uncle Bob) đề xuất, nhằm tạo ra các hệ thống có tính độc lập cao, dễ kiểm thử và bảo trì [7]. Kiến trúc này tổ chức code thành các tầng đồng tâm, với business logic ở trung tâm và các dependencies hướng vào trong.

**[CHÈN HÌNH 2.4: Mô hình Clean Architecture]**

**Các tầng trong Clean Architecture:**

**Entities Layer (Core):**
- Chứa business objects và enterprise business rules
- Độc lập hoàn toàn với framework và external concerns
- Ví dụ: User, Movie, Ticket, Theater entities

**Use Cases Layer (Application Business Rules):**
- Chứa application-specific business rules
- Orchestrate data flow giữa entities và external interfaces
- Ví dụ: BookTicketUseCase, AuthenticateUserUseCase

**Interface Adapters Layer:**
- Convert data giữa use cases và external agencies
- Bao gồm Controllers, Presenters, Gateways
- Ví dụ: MovieController, AuthController, MovieRepository

**Frameworks & Drivers Layer:**
- External frameworks và tools
- Database, Web framework, UI framework
- Ví dụ: Flutter widgets, Firebase services, HTTP clients

**Lợi ích của Clean Architecture:**

**Testability:** Mỗi tầng có thể test độc lập với mock dependencies.

**Independence:** Business logic không phụ thuộc vào UI, database hay external frameworks.

**Maintainability:** Thay đổi ở một tầng không ảnh hưởng đến tầng khác.

**Flexibility:** Dễ dàng thay đổi database, UI framework mà không ảnh hưởng business logic.

#### 2.4.2. Model-View-Controller (MVC) với GetX

**GetX Pattern:**
GetX framework implement một biến thể của MVC pattern được tối ưu cho Flutter development, kết hợp state management, dependency injection và route management trong một package duy nhất [8].

**Components của GetX Pattern:**

**Model:** Đại diện cho data structures và business entities.
```dart
class Movie {
  final int id;
  final String title;
  final String overview;
  final double rating;

  Movie({required this.id, required this.title, required this.overview, required this.rating});
}
```

**View:** Flutter widgets hiển thị UI và nhận user input.
```dart
class MovieListPage extends StatelessWidget {
  final MovieController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return Obx(() => ListView.builder(
      itemCount: controller.movies.length,
      itemBuilder: (context, index) => MovieCard(movie: controller.movies[index]),
    ));
  }
}
```

**Controller:** Quản lý state và business logic, kết nối Model và View.
```dart
class MovieController extends GetxController {
  var movies = <Movie>[].obs;
  var isLoading = false.obs;

  Future<void> loadMovies() async {
    isLoading.value = true;
    movies.value = await MovieService.getMovies();
    isLoading.value = false;
  }
}
```

**Ưu điểm của GetX Pattern:**

**Reactive Programming:** Automatic UI updates khi state thay đổi.

**Minimal Boilerplate:** Ít code boilerplate so với các state management khác.

**Performance:** High performance với smart rebuilds.

**Memory Management:** Automatic disposal của controllers khi không sử dụng.

#### 2.4.3. Repository Pattern

**Mục đích Repository Pattern:**
Repository pattern tạo ra một abstraction layer giữa business logic và data access logic, cho phép thay đổi data source mà không ảnh hưởng đến business logic [9].

**Implementation trong dự án:**

**Abstract Repository:**
```dart
abstract class MovieRepository {
  Future<List<Movie>> getMovies();
  Future<Movie> getMovieById(int id);
  Future<List<Movie>> searchMovies(String query);
}
```

**Concrete Implementation:**
```dart
class FirebaseMovieRepository implements MovieRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  @override
  Future<List<Movie>> getMovies() async {
    final snapshot = await _firestore.collection('movies').get();
    return snapshot.docs.map((doc) => Movie.fromFirestore(doc)).toList();
  }
}

class TMDBMovieRepository implements MovieRepository {
  final http.Client _client = http.Client();

  @override
  Future<List<Movie>> getMovies() async {
    final response = await _client.get(Uri.parse('$tmdbBaseUrl/movie/popular'));
    final data = json.decode(response.body);
    return data['results'].map<Movie>((json) => Movie.fromTMDB(json)).toList();
  }
}
```

**Lợi ích:**

**Flexibility:** Dễ dàng switch giữa các data sources (Firebase, TMDB, local cache).

**Testability:** Mock repositories cho unit testing.

**Separation of Concerns:** Business logic tách biệt khỏi data access.

#### 2.4.4. Observer Pattern

**Real-time Updates với Observer Pattern:**
Observer pattern được sử dụng rộng rãi trong dự án để implement real-time features, đặc biệt cho seat selection và notifications.

**Firebase Streams:**
```dart
class SeatReservationService {
  Stream<Map<String, SeatStatus>> getSeatStatus(String showtimeId) {
    return FirebaseDatabase.instance
        .ref('seat_reservations/$showtimeId')
        .onValue
        .map((event) => _parseSeatStatus(event.snapshot.value));
  }
}
```

**GetX Reactive Variables:**
```dart
class BookingController extends GetxController {
  var selectedSeats = <String>[].obs;
  var seatStatus = <String, SeatStatus>{}.obs;

  @override
  void onInit() {
    super.onInit();
    // Listen to seat status changes
    SeatReservationService.getSeatStatus(showtimeId).listen((status) {
      seatStatus.value = status;
    });
  }
}
```

### 2.5. Bảo mật và thanh toán trực tuyến

#### 2.5.1. Chuẩn bảo mật PCI DSS

**Payment Card Industry Data Security Standard (PCI DSS):**
PCI DSS là một tập hợp các yêu cầu bảo mật được thiết kế để đảm bảo rằng tất cả các công ty xử lý, lưu trữ hoặc truyền tải thông tin thẻ tín dụng duy trì một môi trường an toàn [10].

**[CHÈN HÌNH 2.5: Quy trình thanh toán trực tuyến an toàn]**

**12 yêu cầu chính của PCI DSS:**

1. **Firewall Configuration:** Cài đặt và duy trì firewall để bảo vệ cardholder data
2. **Default Passwords:** Không sử dụng default passwords và security parameters
3. **Cardholder Data Protection:** Bảo vệ stored cardholder data
4. **Encrypted Transmission:** Mã hóa transmission của cardholder data qua public networks
5. **Antivirus Software:** Sử dụng và update antivirus software
6. **Secure Systems:** Phát triển và maintain secure systems và applications
7. **Access Control:** Restrict access đến cardholder data theo business need-to-know
8. **Unique IDs:** Assign unique ID cho mỗi person có computer access
9. **Physical Access:** Restrict physical access đến cardholder data
10. **Network Monitoring:** Track và monitor tất cả access đến network resources
11. **Security Testing:** Regularly test security systems và processes
12. **Information Security Policy:** Maintain policy để address information security

**Implementation trong dự án:**

**Tokenization:** Thay thế sensitive card data bằng tokens.

**Encryption:** Mã hóa tất cả cardholder data transmission.

**Access Control:** Implement strict access controls cho payment data.

**Audit Logging:** Log tất cả payment-related activities.

#### 2.5.2. OAuth 2.0 và JWT

**OAuth 2.0 Authorization Framework:**
OAuth 2.0 là một authorization framework cho phép third-party applications có limited access đến user accounts [11]. Trong dự án, OAuth 2.0 được sử dụng cho Google Sign-In integration.

**OAuth 2.0 Flow:**
1. **Authorization Request:** Client redirect user đến authorization server
2. **Authorization Grant:** User authorize client và receive authorization code
3. **Access Token Request:** Client exchange authorization code cho access token
4. **Access Token Response:** Authorization server return access token
5. **Protected Resource Access:** Client sử dụng access token để access protected resources

**JSON Web Tokens (JWT):**
JWT là một compact, URL-safe means để represent claims giữa hai parties [12]. Firebase Authentication sử dụng JWT cho user authentication tokens.

**JWT Structure:**
- **Header:** Chứa token type và signing algorithm
- **Payload:** Chứa claims (user data, permissions, expiration)
- **Signature:** Verify token integrity và authenticity

**Security Benefits:**
- **Stateless:** Không cần server-side session storage
- **Scalable:** Tokens có thể verify independently
- **Secure:** Cryptographically signed và optionally encrypted

#### 2.5.3. Firebase Security Rules

**Firestore Security Rules:**
Firebase Security Rules provide server-side authorization và data validation cho Firebase services [13].

**Rule Structure:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow users to read/write their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }

    // Allow authenticated users to read movies
    match /movies/{movieId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && isAdmin();
    }

    // Helper functions
    function isAdmin() {
      return request.auth.token.role == 'admin';
    }
  }
}
```

**Security Features:**

**Authentication-based Rules:** Access control dựa trên user authentication status.

**Role-based Authorization:** Different permissions cho different user roles.

**Data Validation:** Validate data structure và content trước khi write.

**Field-level Security:** Granular control over individual fields.

#### 2.5.4. PayPal Integration Security

**PayPal REST API Security:**
PayPal cung cấp secure payment processing với multiple layers của security measures [14].

**Security Measures:**

**HTTPS Encryption:** Tất cả API calls được encrypt với TLS 1.2+.

**OAuth 2.0 Authentication:** Secure authentication cho API access.

**Webhook Verification:** Verify webhook authenticity với signature validation.

**Fraud Protection:** Built-in fraud detection và risk management.

**Implementation Strategy:**

**Server-side Processing:** Payment logic được xử lý trên Cloud Functions để tránh client-side manipulation.

**Webhook Handling:** Sử dụng webhooks để receive payment status updates securely.

**Error Handling:** Comprehensive error handling cho payment failures và edge cases.

**Audit Trail:** Log tất cả payment activities cho compliance và debugging.

---

## CHƯƠNG 3: PHÂN TÍCH VÀ THIẾT KẾ HỆ THỐNG

### 3.1. Phân tích yêu cầu hệ thống

#### 3.1.1. Phân tích stakeholders chi tiết

**Primary Stakeholders:**

**End Users (Khách hàng xem phim):**
- **Demographics:** 18-45 tuổi, thu nhập trung bình+, sống tại thành phố
- **Behavior:** Xem phim 2-4 lần/tháng, sử dụng smartphone chủ yếu
- **Needs:** Đặt vé nhanh, chọn ghế, thanh toán tiện lợi, thông tin phim đầy đủ
- **Pain Points:** Xếp hàng mua vé, không biết ghế trống, so sánh giá khó khăn

**Cinema Operators (Rạp chiếu phim):**
- **Types:** Chuỗi rạp lớn (CGV, Galaxy), rạp độc lập, rạp cao cấp
- **Needs:** Tăng doanh thu, giảm chi phí vận hành, quản lý lịch chiếu hiệu quả
- **Pain Points:** Cạnh tranh gay gắt, quản lý manual, dự đoán nhu cầu khó

**Secondary Stakeholders:**

**Movie Distributors:** Cần platform để promote phim và track performance.

**Payment Providers:** PayPal, VNPay cần integration secure và compliant.

**Technology Partners:** TMDB API, Firebase services, third-party libraries.

**[CHÈN HÌNH 3.1: Use case diagram tổng thể hệ thống]**

---

*[Tiếp tục với phần 3.1.2...]*
