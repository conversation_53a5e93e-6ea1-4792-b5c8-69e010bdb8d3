## 10. MODULE QUẢN LÝ PHIM

### 10.1 Tổng quan Movie Management

Module quản lý phim tích hợp TMDB API để cung cấp thông tin phim phong phú, kết hợp với Firebase Firestore để lưu trữ dữ liệu local và quản lý lịch chiếu.

**[CHÈN ẢNH: Movie Management Architecture - Hình 10.1]**
*Kiến trúc tích hợp TMDB API và Firebase cho movie management*

#### 10.1.1 TMDB Integration Strategy

**API Integration Architecture:**
Module quản lý phim sử dụng hybrid approach kết hợp TMDB API và Firebase Firestore:

**TMDB API Usage:**
- **Movie Discovery:** L<PERSON>y danh sách phim đang chiếu, sắp chiếu
- **Movie Details:** Thông tin chi tiết, cast, crew, trailers
- **Search Functionality:** Tìm kiếm phim theo tên, thể loại
- **Image Assets:** Poster, backdrop images với multiple resolutions

**Data Flow Strategy:**
1. **Primary Source:** TMDB API cho dữ liệu real-time
2. **Local Cache:** Firestore cho offline access và performance
3. **Fallback Mechanism:** Cached data khi API unavailable
4. **Sync Strategy:** Daily sync cho new releases

**Performance Optimization:**
- **Image Caching:** CDN caching cho movie posters
- **Pagination:** Load movies theo batches (20 items/page)
- **Lazy Loading:** Load details khi user xem movie
- **Background Sync:** Update cache trong background

**Localization Support:**
- **Vietnamese Content:** API calls với language=vi-VN
- **Fallback Language:** English content nếu Vietnamese không có
- **Regional Content:** Region=VN cho phim phù hợp thị trường Việt Nam
```

#### 10.1.2 Movie Detail Implementation

```dart
// lib/view/page/movie/movie_detail_page.dart
class MovieDetailPage extends StatelessWidget {
  final Movie movie;
  final MovieController controller = Get.find();

  MovieDetailPage({required this.movie});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        slivers: [
          // Hero section with backdrop
          SliverAppBar(
            expandedHeight: 300,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: Stack(
                fit: StackFit.expand,
                children: [
                  CachedNetworkImage(
                    imageUrl: movie.backdropUrl,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[300],
                      child: Center(child: CircularProgressIndicator()),
                    ),
                  ),
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withOpacity(0.7),
                        ],
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 20,
                    left: 20,
                    right: 20,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        // Movie poster
                        Container(
                          width: 120,
                          height: 180,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black26,
                                blurRadius: 10,
                                offset: Offset(0, 4),
                              ),
                            ],
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: CachedNetworkImage(
                              imageUrl: movie.posterUrl,
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                        SizedBox(width: 16),
                        // Movie info
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                movie.title,
                                style: AppTypography.headlineMedium.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              SizedBox(height: 8),
                              Row(
                                children: [
                                  Icon(Icons.star, color: Colors.amber, size: 20),
                                  SizedBox(width: 4),
                                  Text(
                                    movie.rating.toStringAsFixed(1),
                                    style: TextStyle(color: Colors.white),
                                  ),
                                  SizedBox(width: 16),
                                  Icon(Icons.access_time, color: Colors.white, size: 20),
                                  SizedBox(width: 4),
                                  Text(
                                    '${movie.runtime} phút',
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ],
                              ),
                              SizedBox(height: 8),
                              Wrap(
                                spacing: 8,
                                children: movie.genres.map((genre) => Chip(
                                  label: Text(genre, style: TextStyle(fontSize: 12)),
                                  backgroundColor: AppColors.secondary.withOpacity(0.8),
                                )).toList(),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Movie details content
          SliverToBoxAdapter(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: AppButton(
                          text: 'Đặt vé',
                          onPressed: () => _bookTicket(),
                          icon: Icon(Icons.confirmation_number, size: 20),
                        ),
                      ),
                      SizedBox(width: 12),
                      IconButton(
                        onPressed: () => controller.toggleFavorite(movie),
                        icon: Obx(() => Icon(
                          controller.isFavorite(movie) 
                              ? Icons.favorite 
                              : Icons.favorite_border,
                          color: AppColors.error,
                        )),
                      ),
                      IconButton(
                        onPressed: () => _shareMovie(),
                        icon: Icon(Icons.share),
                      ),
                    ],
                  ),
                  
                  SizedBox(height: 24),
                  
                  // Synopsis
                  Text(
                    'Tóm tắt',
                    style: AppTypography.headlineSmall,
                  ),
                  SizedBox(height: 8),
                  ReadMoreText(
                    movie.overview,
                    trimLines: 3,
                    colorClickableText: AppColors.primary,
                    trimMode: TrimMode.Line,
                    trimCollapsedText: ' Xem thêm',
                    trimExpandedText: ' Thu gọn',
                  ),
                  
                  SizedBox(height: 24),
                  
                  // Trailer section
                  if (movie.trailerUrls.isNotEmpty) ...[
                    Text(
                      'Trailer',
                      style: AppTypography.headlineSmall,
                    ),
                    SizedBox(height: 12),
                    Container(
                      height: 200,
                      child: ListView.builder(
                        scrollDirection: Axis.horizontal,
                        itemCount: movie.trailerUrls.length,
                        itemBuilder: (context, index) {
                          return TrailerCard(
                            trailerUrl: movie.trailerUrls[index],
                            onTap: () => _playTrailer(movie.trailerUrls[index]),
                          );
                        },
                      ),
                    ),
                  ],
                  
                  SizedBox(height: 24),
                  
                  // Cast section
                  Text(
                    'Diễn viên',
                    style: AppTypography.headlineSmall,
                  ),
                  SizedBox(height: 12),
                  Container(
                    height: 120,
                    child: ListView.builder(
                      scrollDirection: Axis.horizontal,
                      itemCount: movie.cast.length,
                      itemBuilder: (context, index) {
                        final actor = movie.cast[index];
                        return CastCard(actor: actor);
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _bookTicket() {
    Get.to(() => TheaterSelectionPage(movie: movie));
  }

  void _playTrailer(String trailerUrl) {
    Get.to(() => TrailerPlayerPage(trailerUrl: trailerUrl));
  }

  void _shareMovie() {
    Share.share(
      'Xem phim ${movie.title} cùng tôi trên Đớp Phim!\n'
      'Đánh giá: ${movie.rating}/10\n'
      'Tải app: https://dopphim.app',
    );
  }
}
```

**[CHÈN ẢNH: Movie Detail Screen - Hình 10.2]**
*Màn hình chi tiết phim với hero image, thông tin và trailer*

## 11. MODULE ĐẶT VÉ VÀ THANH TOÁN

### 11.1 Booking Workflow Implementation

#### 11.1.1 5-Step Booking Process

```dart
// lib/controllers/booking_controller.dart
class BookingController extends GetxController {
  // Booking state
  var currentStep = 0.obs;
  var selectedMovie = Rx<Movie?>(null);
  var selectedTheater = Rx<Theater?>(null);
  var selectedShowtime = Rx<Showtime?>(null);
  var selectedSeats = <String>[].obs;
  var totalAmount = 0.0.obs;

  // Step navigation
  void nextStep() {
    if (currentStep.value < 4) {
      currentStep.value++;
    }
  }

  void previousStep() {
    if (currentStep.value > 0) {
      currentStep.value--;
    }
  }

  // Step 4: Seat Selection with Real-time Updates
  Future<void> selectSeat(String seatId) async {
    if (selectedSeats.contains(seatId)) {
      // Deselect seat
      selectedSeats.remove(seatId);
      await _releaseSeatReservation(seatId);
    } else {
      // Select seat
      if (selectedSeats.length >= 8) {
        Get.snackbar('Giới hạn', 'Chỉ có thể chọn tối đa 8 ghế');
        return;
      }
      
      final success = await _reserveSeat(seatId);
      if (success) {
        selectedSeats.add(seatId);
      }
    }
    
    _calculateTotalAmount();
  }

  Future<bool> _reserveSeat(String seatId) async {
    try {
      final result = await FirebaseFunctions.instance
          .httpsCallable('reserveSeat')
          .call({
        'showtimeId': selectedShowtime.value!.id,
        'seatId': seatId,
      });
      
      return result.data['success'] == true;
    } catch (e) {
      Get.snackbar('Lỗi', 'Không thể đặt ghế: $e');
      return false;
    }
  }

  void _calculateTotalAmount() {
    double total = 0;
    for (final seatId in selectedSeats) {
      final seatType = _getSeatType(seatId);
      total += _getSeatPrice(seatType);
    }
    totalAmount.value = total;
  }
}
```

#### 11.1.2 Real-time Seat Selection

```dart
// lib/view/widgets/booking/seat_map.dart
class SeatMap extends StatelessWidget {
  final Showtime showtime;
  final BookingController controller = Get.find();

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<DatabaseEvent>(
      stream: FirebaseDatabase.instance
          .ref('seat_reservations/${showtime.id}')
          .onValue,
      builder: (context, snapshot) {
        Map<String, dynamic> reservations = {};
        
        if (snapshot.hasData && snapshot.data!.snapshot.value != null) {
          reservations = Map<String, dynamic>.from(
            snapshot.data!.snapshot.value as Map
          );
        }

        return Container(
          padding: EdgeInsets.all(16),
          child: Column(
            children: [
              // Screen indicator
              Container(
                width: double.infinity,
                height: 4,
                margin: EdgeInsets.only(bottom: 24),
                decoration: BoxDecoration(
                  color: AppColors.primary,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Text(
                'MÀN HÌNH',
                style: AppTypography.labelMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              
              SizedBox(height: 32),
              
              // Seat grid
              Expanded(
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: showtime.screen.seatsPerRow,
                    crossAxisSpacing: 4,
                    mainAxisSpacing: 4,
                  ),
                  itemCount: showtime.screen.totalSeats,
                  itemBuilder: (context, index) {
                    final seatId = _getSeatId(index);
                    final seatStatus = _getSeatStatus(seatId, reservations);
                    
                    return GestureDetector(
                      onTap: () => _onSeatTap(seatId, seatStatus),
                      child: AnimatedContainer(
                        duration: Duration(milliseconds: 200),
                        decoration: BoxDecoration(
                          color: _getSeatColor(seatStatus),
                          borderRadius: BorderRadius.circular(4),
                          border: Border.all(
                            color: seatStatus == SeatStatus.selected
                                ? AppColors.primary
                                : Colors.transparent,
                            width: 2,
                          ),
                        ),
                        child: Center(
                          child: Text(
                            seatId,
                            style: TextStyle(
                              color: _getSeatTextColor(seatStatus),
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
              
              // Legend
              SizedBox(height: 16),
              _buildSeatLegend(),
            ],
          ),
        );
      },
    );
  }

  SeatStatus _getSeatStatus(String seatId, Map<String, dynamic> reservations) {
    if (controller.selectedSeats.contains(seatId)) {
      return SeatStatus.selected;
    }
    
    if (showtime.bookedSeats.contains(seatId)) {
      return SeatStatus.booked;
    }
    
    if (reservations.containsKey(seatId)) {
      final reservation = reservations[seatId];
      final expiresAt = DateTime.parse(reservation['expiresAt']);
      if (DateTime.now().isBefore(expiresAt)) {
        return SeatStatus.reserved;
      }
    }
    
    return SeatStatus.available;
  }

  Color _getSeatColor(SeatStatus status) {
    switch (status) {
      case SeatStatus.available:
        return AppColors.seatAvailable;
      case SeatStatus.selected:
        return AppColors.seatSelected;
      case SeatStatus.reserved:
        return AppColors.seatReserved;
      case SeatStatus.booked:
        return AppColors.seatBooked;
    }
  }
}

enum SeatStatus { available, selected, reserved, booked }
```

**[CHÈN ẢNH: Seat Selection Interface - Hình 11.1]**
*Giao diện chọn ghế với real-time updates*

### 11.2 PayPal Payment Integration

#### 11.2.1 Payment Processing

```dart
// lib/services/payment_service.dart
class PaymentService extends GetxService {
  Future<PaymentResult> processPayment({
    required String ticketId,
    required double amount,
    required String currency,
  }) async {
    try {
      // Step 1: Create PayPal order
      final paypalOrder = await _createPayPalOrder(amount, currency, ticketId);
      
      // Step 2: Show PayPal WebView
      final paymentResult = await Get.to(() => PayPalWebView(
        approvalUrl: paypalOrder.approvalUrl,
        orderId: paypalOrder.orderId,
      ));
      
      if (paymentResult?.success == true) {
        // Step 3: Capture payment via Cloud Function
        final captureResult = await _capturePayment(paypalOrder.orderId, ticketId);
        
        if (captureResult.success) {
          return PaymentResult.success(
            transactionId: captureResult.transactionId,
            amount: amount,
          );
        }
      }
      
      return PaymentResult.failed('Payment was cancelled or failed');
      
    } catch (e) {
      return PaymentResult.failed('Payment error: $e');
    }
  }

  Future<PayPalOrder> _createPayPalOrder(double amount, String currency, String ticketId) async {
    final result = await FirebaseFunctions.instance
        .httpsCallable('createPayPalOrder')
        .call({
      'amount': (amount / 23000).toStringAsFixed(2), // VND to USD
      'currency': 'USD',
      'ticketId': ticketId,
    });

    if (result.data['success']) {
      return PayPalOrder(
        orderId: result.data['orderId'],
        approvalUrl: result.data['approvalUrl'],
      );
    }
    
    throw Exception(result.data['error']);
  }
}
```

#### 11.2.2 PayPal WebView Implementation

```dart
// lib/view/page/payment/paypal_webview.dart
class PayPalWebView extends StatefulWidget {
  final String approvalUrl;
  final String orderId;

  PayPalWebView({required this.approvalUrl, required this.orderId});

  @override
  _PayPalWebViewState createState() => _PayPalWebViewState();
}

class _PayPalWebViewState extends State<PayPalWebView> {
  late WebViewController _controller;
  bool _isLoading = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text('Thanh toán PayPal'),
        leading: IconButton(
          icon: Icon(Icons.close),
          onPressed: () => Get.back(result: PaymentResult.cancelled()),
        ),
      ),
      body: Stack(
        children: [
          WebView(
            initialUrl: widget.approvalUrl,
            javascriptMode: JavascriptMode.unrestricted,
            onWebViewCreated: (controller) {
              _controller = controller;
            },
            onPageStarted: (url) {
              setState(() {
                _isLoading = true;
              });
              _checkUrlForCompletion(url);
            },
            onPageFinished: (url) {
              setState(() {
                _isLoading = false;
              });
            },
          ),
          if (_isLoading)
            Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Đang tải PayPal...'),
                ],
              ),
            ),
        ],
      ),
    );
  }

  void _checkUrlForCompletion(String url) {
    if (url.contains('payment/success')) {
      // Extract payment details from URL
      final uri = Uri.parse(url);
      final paymentId = uri.queryParameters['paymentId'];
      final payerId = uri.queryParameters['PayerID'];
      
      Get.back(result: PaymentResult.success(
        transactionId: paymentId ?? widget.orderId,
        amount: 0, // Will be filled by the calling function
      ));
    } else if (url.contains('payment/cancel')) {
      Get.back(result: PaymentResult.cancelled());
    }
  }
}
```

**[CHÈN ẢNH: PayPal Payment Flow - Hình 11.2]**
*Luồng thanh toán PayPal từ order creation đến completion*

## 12. MODULE QUẢN LÝ RẠP VÀ LỊCH CHIẾU

### 12.1 Theater Management

```dart
// lib/controllers/admin/theater_controller.dart
class TheaterController extends GetxController {
  var theaters = <Theater>[].obs;
  var isLoading = false.obs;

  Future<void> addTheater(Theater theater) async {
    isLoading.value = true;
    
    try {
      await FirebaseFirestore.instance
          .collection('theaters')
          .doc(theater.id)
          .set(theater.toFirestore());
      
      theaters.add(theater);
      
      Get.snackbar(
        'Thành công',
        'Đã thêm rạp chiếu mới',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar('Lỗi', 'Không thể thêm rạp: $e');
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> bulkImportTheaters(File excelFile) async {
    isLoading.value = true;
    
    try {
      final bytes = await excelFile.readAsBytes();
      final excel = Excel.decodeBytes(bytes);
      
      final batch = FirebaseFirestore.instance.batch();
      int importCount = 0;
      
      for (final table in excel.tables.keys) {
        final sheet = excel.tables[table]!;
        
        for (int row = 1; row < sheet.maxRows; row++) { // Skip header
          final theater = _parseTheaterFromRow(sheet, row);
          if (theater != null) {
            final docRef = FirebaseFirestore.instance
                .collection('theaters')
                .doc(theater.id);
            batch.set(docRef, theater.toFirestore());
            importCount++;
          }
        }
      }
      
      await batch.commit();
      await loadTheaters(); // Refresh list
      
      Get.snackbar(
        'Import thành công',
        'Đã import $importCount rạp chiếu',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar('Lỗi import', 'Không thể import file: $e');
    } finally {
      isLoading.value = false;
    }
  }
}
```

### 12.2 Schedule Management

```dart
// lib/controllers/admin/schedule_controller.dart
class ScheduleController extends GetxController {
  Future<void> createShowtime(Showtime showtime) async {
    try {
      // Validate no conflicts
      final conflicts = await _checkScheduleConflicts(showtime);
      if (conflicts.isNotEmpty) {
        Get.snackbar(
          'Xung đột lịch chiếu',
          'Phòng chiếu đã có lịch trong thời gian này',
          backgroundColor: AppColors.warning,
        );
        return;
      }

      await FirebaseFirestore.instance
          .collection('showtimes')
          .doc(showtime.id)
          .set(showtime.toFirestore());

      Get.snackbar(
        'Thành công',
        'Đã tạo lịch chiếu mới',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
      );
    } catch (e) {
      Get.snackbar('Lỗi', 'Không thể tạo lịch chiếu: $e');
    }
  }

  Future<List<Showtime>> _checkScheduleConflicts(Showtime newShowtime) async {
    final query = await FirebaseFirestore.instance
        .collection('showtimes')
        .where('screenId', isEqualTo: newShowtime.screenId)
        .where('startTime', isGreaterThanOrEqualTo: newShowtime.startTime.subtract(Duration(hours: 4)))
        .where('startTime', isLessThanOrEqualTo: newShowtime.endTime.add(Duration(hours: 4)))
        .get();

    return query.docs
        .map((doc) => Showtime.fromFirestore(doc))
        .where((showtime) => _hasTimeOverlap(showtime, newShowtime))
        .toList();
  }
}
```

**[CHÈN ẢNH: Admin Dashboard - Hình 12.1]**
*Dashboard quản trị với theater management và schedule creation*

---

*Phần tiếp theo: Các module còn lại và kết luận*
