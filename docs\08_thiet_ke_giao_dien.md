## 8. THIẾT KẾ GIAO DIỆN NGƯỜI DÙNG

### 8.1 Tổng quan thiết kế UI/UX

Giao diện "Đớp Phim" đư<PERSON><PERSON> thiết kế theo Material Design 3 guidelines với focus vào user experience tối ưu cho việc đặt vé xem phim. Thiết kế ưu tiên tính đơn giản, trực quan và accessibility cho mọi đối tượng người dùng.

**[CHÈN ẢNH: UI Design System Overview - Hình 8.1]**
*Tổng quan design system với color palette, typography và components*

#### 8.1.1 Design Principles

**User-Centered Design:**
- Đặt nhu cầu người dùng làm trung tâm
- Minimize cognitive load
- Clear information hierarchy
- Intuitive navigation patterns

**Accessibility First:**
- WCAG 2.1 AA compliance
- Screen reader support
- High contrast ratios
- Touch target sizes ≥ 44dp
- Keyboard navigation support

**Performance-Oriented:**
- Lazy loading cho images
- Skeleton screens cho loading states
- Optimized animations (60fps)
- Minimal re-renders

**Responsive Design:**
- Mobile-first approach
- Adaptive layouts cho tablets
- Desktop-friendly web version
- Consistent experience across platforms

### 8.2 Design System

#### 8.2.1 Color Palette

**Primary Colors:**
```dart
// lib/core/theme/app_colors.dart
class AppColors {
  // Primary Brand Colors
  static const Color primary = Color(0xFF1976D2);      // Blue 700
  static const Color primaryVariant = Color(0xFF1565C0); // Blue 800
  static const Color secondary = Color(0xFFFFC107);     // Amber 500
  static const Color secondaryVariant = Color(0xFFFF8F00); // Amber 700

  // Surface Colors
  static const Color surface = Color(0xFFFFFFFF);       // White
  static const Color background = Color(0xFFF5F5F5);    // Grey 100
  static const Color card = Color(0xFFFFFFFF);          // White

  // Text Colors
  static const Color onPrimary = Color(0xFFFFFFFF);     // White
  static const Color onSecondary = Color(0xFF000000);   // Black
  static const Color onSurface = Color(0xFF212121);     // Grey 900
  static const Color onBackground = Color(0xFF212121);  // Grey 900

  // Status Colors
  static const Color success = Color(0xFF4CAF50);       // Green 500
  static const Color warning = Color(0xFFFF9800);       // Orange 500
  static const Color error = Color(0xFFF44336);         // Red 500
  static const Color info = Color(0xFF2196F3);          // Blue 500

  // Seat Colors
  static const Color seatAvailable = Color(0xFFE0E0E0); // Grey 300
  static const Color seatSelected = Color(0xFF1976D2);  // Primary
  static const Color seatReserved = Color(0xFFFFC107);  // Secondary
  static const Color seatBooked = Color(0xFFF44336);    // Error
}
```

**Dark Theme Support:**
```dart
class AppColorsDark {
  static const Color primary = Color(0xFF90CAF9);       // Blue 200
  static const Color surface = Color(0xFF121212);       // Dark surface
  static const Color background = Color(0xFF000000);    // Black
  static const Color onSurface = Color(0xFFFFFFFF);     // White
  // ... other dark theme colors
}
```

**[CHÈN ẢNH: Color Palette Showcase - Hình 8.2]**
*Bảng màu chính và ứng dụng trong các components*

#### 8.2.2 Typography System

**Font Configuration:**
```dart
// lib/core/theme/app_typography.dart
class AppTypography {
  static const String fontFamily = 'Roboto';
  
  // Display Styles
  static const TextStyle displayLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 57,
    fontWeight: FontWeight.w400,
    letterSpacing: -0.25,
    height: 1.12,
  );
  
  static const TextStyle displayMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 45,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.16,
  );

  // Headline Styles
  static const TextStyle headlineLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 32,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.25,
  );

  static const TextStyle headlineMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 28,
    fontWeight: FontWeight.w400,
    letterSpacing: 0,
    height: 1.29,
  );

  // Body Styles
  static const TextStyle bodyLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 16,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.5,
    height: 1.5,
  );

  static const TextStyle bodyMedium = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w400,
    letterSpacing: 0.25,
    height: 1.43,
  );

  // Label Styles
  static const TextStyle labelLarge = TextStyle(
    fontFamily: fontFamily,
    fontSize: 14,
    fontWeight: FontWeight.w500,
    letterSpacing: 0.1,
    height: 1.43,
  );
}
```

**Vietnamese Font Support:**
```dart
// Google Fonts integration for Vietnamese
static TextStyle getVietnameseFont({
  required double fontSize,
  FontWeight fontWeight = FontWeight.normal,
  Color? color,
}) {
  return GoogleFonts.roboto(
    fontSize: fontSize,
    fontWeight: fontWeight,
    color: color,
    // Ensures proper Vietnamese character rendering
    fontFeatures: [FontFeature.enable('kern')],
  );
}
```

#### 8.2.3 Component Library

**Button Components:**
```dart
// lib/view/widgets/common/app_button.dart
class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final ButtonType type;
  final ButtonSize size;
  final bool isLoading;
  final Widget? icon;

  const AppButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.type = ButtonType.primary,
    this.size = ButtonSize.medium,
    this.isLoading = false,
    this.icon,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: _getHeight(),
      child: ElevatedButton(
        onPressed: isLoading ? null : onPressed,
        style: _getButtonStyle(context),
        child: isLoading
            ? SizedBox(
                height: 20,
                width: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(
                    _getTextColor(context),
                  ),
                ),
              )
            : Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (icon != null) ...[
                    icon!,
                    SizedBox(width: 8),
                  ],
                  Text(
                    text,
                    style: _getTextStyle(context),
                  ),
                ],
              ),
      ),
    );
  }

  double _getHeight() {
    switch (size) {
      case ButtonSize.small:
        return 32;
      case ButtonSize.medium:
        return 40;
      case ButtonSize.large:
        return 48;
    }
  }

  ButtonStyle _getButtonStyle(BuildContext context) {
    switch (type) {
      case ButtonType.primary:
        return ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: AppColors.onPrimary,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        );
      case ButtonType.secondary:
        return ElevatedButton.styleFrom(
          backgroundColor: AppColors.secondary,
          foregroundColor: AppColors.onSecondary,
          elevation: 1,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        );
      case ButtonType.outline:
        return OutlinedButton.styleFrom(
          foregroundColor: AppColors.primary,
          side: BorderSide(color: AppColors.primary),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        );
    }
  }
}

enum ButtonType { primary, secondary, outline }
enum ButtonSize { small, medium, large }
```

**[CHÈN ẢNH: Component Library Showcase - Hình 8.3]**
*Thư viện components với buttons, cards, inputs và các elements khác*

### 8.3 Screen Designs

#### 8.3.1 Authentication Screens

**Splash Screen:**
```dart
// lib/view/page/auth/splash_page.dart
class SplashPage extends StatefulWidget {
  @override
  _SplashPageState createState() => _SplashPageState();
}

class _SplashPageState extends State<SplashPage> 
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _bannerController;
  late Animation<double> _logoAnimation;
  late Animation<Offset> _bannerAnimation;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _startSplashSequence();
  }

  void _initAnimations() {
    _logoController = AnimationController(
      duration: Duration(milliseconds: 1500),
      vsync: this,
    );
    
    _bannerController = AnimationController(
      duration: Duration(milliseconds: 2000),
      vsync: this,
    );

    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));

    _bannerAnimation = Tween<Offset>(
      begin: Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _bannerController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.primary,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(
              flex: 2,
              child: Center(
                child: AnimatedBuilder(
                  animation: _logoAnimation,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _logoAnimation.value,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Container(
                            width: 120,
                            height: 120,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(24),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.black26,
                                  blurRadius: 10,
                                  offset: Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Icon(
                              Icons.movie,
                              size: 60,
                              color: AppColors.primary,
                            ),
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Đớp Phim',
                            style: AppTypography.headlineLarge.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            'Đặt vé xem phim dễ dàng',
                            style: AppTypography.bodyMedium.copyWith(
                              color: Colors.white70,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
            Expanded(
              flex: 1,
              child: SlideTransition(
                position: _bannerAnimation,
                child: MovieBannerCarousel(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

**Login Screen:**
```dart
// lib/view/page/auth/login_page.dart
class LoginPage extends StatelessWidget {
  final AuthController authController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Spacer(),
              // Logo section
              Center(
                child: Column(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      decoration: BoxDecoration(
                        color: AppColors.primary,
                        borderRadius: BorderRadius.circular(16),
                      ),
                      child: Icon(
                        Icons.movie,
                        size: 40,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 16),
                    Text(
                      'Chào mừng trở lại',
                      style: AppTypography.headlineMedium,
                    ),
                    Text(
                      'Đăng nhập để tiếp tục',
                      style: AppTypography.bodyMedium.copyWith(
                        color: Colors.grey[600],
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(height: 48),
              
              // Login form
              AppTextField(
                controller: authController.emailController,
                label: 'Email',
                keyboardType: TextInputType.emailAddress,
                prefixIcon: Icons.email_outlined,
                validator: (value) => Validators.email(value),
              ),
              SizedBox(height: 16),
              
              Obx(() => AppTextField(
                controller: authController.passwordController,
                label: 'Mật khẩu',
                obscureText: !authController.isPasswordVisible.value,
                prefixIcon: Icons.lock_outlined,
                suffixIcon: IconButton(
                  icon: Icon(
                    authController.isPasswordVisible.value
                        ? Icons.visibility_off
                        : Icons.visibility,
                  ),
                  onPressed: authController.togglePasswordVisibility,
                ),
                validator: (value) => Validators.password(value),
              )),
              
              SizedBox(height: 8),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                  onPressed: () => Get.to(() => ForgotPasswordPage()),
                  child: Text('Quên mật khẩu?'),
                ),
              ),
              
              SizedBox(height: 24),
              
              // Login button
              Obx(() => AppButton(
                text: 'Đăng nhập',
                onPressed: authController.login,
                isLoading: authController.isLoading.value,
              )),
              
              SizedBox(height: 16),
              
              // Divider
              Row(
                children: [
                  Expanded(child: Divider()),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Text('hoặc'),
                  ),
                  Expanded(child: Divider()),
                ],
              ),
              
              SizedBox(height: 16),
              
              // Google Sign-In
              AppButton(
                text: 'Đăng nhập với Google',
                type: ButtonType.outline,
                icon: Image.asset(
                  'assets/images/google_logo.png',
                  width: 20,
                  height: 20,
                ),
                onPressed: authController.signInWithGoogle,
              ),
              
              Spacer(),
              
              // Register link
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text('Chưa có tài khoản? '),
                  TextButton(
                    onPressed: () => Get.to(() => RegisterPage()),
                    child: Text('Đăng ký ngay'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
```

**[CHÈN ẢNH: Authentication Screens - Hình 8.4]**
*Screenshots của splash, login và register screens*

#### 8.3.2 Home và Movie Discovery

**Home Screen Layout:**
```dart
// lib/view/page/home/<USER>
class HomePage extends StatelessWidget {
  final MovieController movieController = Get.find();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: movieController.refreshData,
        child: CustomScrollView(
          slivers: [
            // App Bar with search
            SliverAppBar(
              expandedHeight: 120,
              floating: true,
              pinned: true,
              backgroundColor: AppColors.primary,
              flexibleSpace: FlexibleSpaceBar(
                title: Text('Đớp Phim'),
                background: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        AppColors.primary,
                        AppColors.primaryVariant,
                      ],
                    ),
                  ),
                ),
              ),
              actions: [
                IconButton(
                  icon: Icon(Icons.search),
                  onPressed: () => Get.to(() => SearchPage()),
                ),
                IconButton(
                  icon: Icon(Icons.notifications_outlined),
                  onPressed: () => Get.to(() => NotificationsPage()),
                ),
              ],
            ),
            
            // Banner carousel
            SliverToBoxAdapter(
              child: Container(
                height: 200,
                child: Obx(() => movieController.bannerMovies.isEmpty
                    ? BannerSkeleton()
                    : MovieBannerCarousel(
                        movies: movieController.bannerMovies,
                      )),
              ),
            ),
            
            // Now Playing section
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SectionHeader(
                      title: 'Phim đang chiếu',
                      onSeeAll: () => Get.to(() => MovieListPage(
                        title: 'Phim đang chiếu',
                        movies: movieController.nowPlayingMovies,
                      )),
                    ),
                    SizedBox(height: 12),
                    Container(
                      height: 280,
                      child: Obx(() => movieController.nowPlayingMovies.isEmpty
                          ? MovieListSkeleton()
                          : ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: movieController.nowPlayingMovies.length,
                              itemBuilder: (context, index) {
                                final movie = movieController.nowPlayingMovies[index];
                                return MovieCard(
                                  movie: movie,
                                  onTap: () => Get.to(() => MovieDetailPage(movie: movie)),
                                );
                              },
                            )),
                    ),
                  ],
                ),
              ),
            ),
            
            // Coming Soon section
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SectionHeader(
                      title: 'Phim sắp chiếu',
                      onSeeAll: () => Get.to(() => MovieListPage(
                        title: 'Phim sắp chiếu',
                        movies: movieController.comingSoonMovies,
                      )),
                    ),
                    SizedBox(height: 12),
                    Container(
                      height: 280,
                      child: Obx(() => movieController.comingSoonMovies.isEmpty
                          ? MovieListSkeleton()
                          : ListView.builder(
                              scrollDirection: Axis.horizontal,
                              itemCount: movieController.comingSoonMovies.length,
                              itemBuilder: (context, index) {
                                final movie = movieController.comingSoonMovies[index];
                                return MovieCard(
                                  movie: movie,
                                  onTap: () => Get.to(() => MovieDetailPage(movie: movie)),
                                );
                              },
                            )),
                    ),
                  ],
                ),
              ),
            ),
            
            // Genres section
            SliverToBoxAdapter(
              child: Padding(
                padding: EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Thể loại phim',
                      style: AppTypography.headlineSmall,
                    ),
                    SizedBox(height: 12),
                    GenreGrid(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

**[CHÈN ẲNH: Home Screen Layout - Hình 8.5]**
*Layout của home screen với banner carousel, movie sections và genre grid*

### 8.4 Responsive Design

#### 8.4.1 Breakpoint System

```dart
// lib/core/responsive/breakpoints.dart
class Breakpoints {
  static const double mobile = 600;
  static const double tablet = 1024;
  static const double desktop = 1440;
  
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < mobile;
  }
  
  static bool isTablet(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    return width >= mobile && width < desktop;
  }
  
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= desktop;
  }
}

class ResponsiveBuilder extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;

  const ResponsiveBuilder({
    Key? key,
    required this.mobile,
    this.tablet,
    this.desktop,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        if (constraints.maxWidth >= Breakpoints.desktop) {
          return desktop ?? tablet ?? mobile;
        } else if (constraints.maxWidth >= Breakpoints.mobile) {
          return tablet ?? mobile;
        } else {
          return mobile;
        }
      },
    );
  }
}
```

#### 8.4.2 Adaptive Layouts

```dart
// lib/view/widgets/common/adaptive_grid.dart
class AdaptiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double spacing;

  const AdaptiveGrid({
    Key? key,
    required this.children,
    this.spacing = 16,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ResponsiveBuilder(
      mobile: GridView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: spacing,
          mainAxisSpacing: spacing,
          childAspectRatio: 0.7,
        ),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      ),
      tablet: GridView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: spacing,
          mainAxisSpacing: spacing,
          childAspectRatio: 0.75,
        ),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      ),
      desktop: GridView.builder(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: spacing,
          mainAxisSpacing: spacing,
          childAspectRatio: 0.8,
        ),
        itemCount: children.length,
        itemBuilder: (context, index) => children[index],
      ),
    );
  }
}
```

**[CHÈN ẢNH: Responsive Design Examples - Hình 8.6]**
*Ví dụ về responsive design trên mobile, tablet và desktop*

---

*Phần tiếp theo: PHẦN III - TRIỂN KHAI CHI TIẾT*
