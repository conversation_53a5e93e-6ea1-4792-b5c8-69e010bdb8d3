## 6. THIẾT KẾ CƠ SỞ DỮ LIỆU

### 6.1 Tổng quan thiết kế database

Hệ thống "Đớp Phim" sử dụng hybrid database approach với Firebase Firestore làm primary database và Firebase Realtime Database cho real-time features. Thiết kế database được tối ưu cho performance, scalability và real-time capabilities.

**[CHÈN ẢNH: Entity Relationship Diagram - Hình 6.1]**
*ERD tổng thể của hệ thống với 12 entities chính*

#### 6.1.1 Database Architecture Strategy

**Firestore (Primary Database):**
- Document-based NoSQL database
- ACID transactions support
- Complex queries với composite indexes
- Offline support với local caching
- Strong consistency

**Realtime Database (Secondary):**
- JSON tree structure
- Real-time synchronization
- Low latency updates
- Simple queries
- Eventually consistent

**Storage Strategy:**
```
Firestore: Structured data, complex relationships
├── Users, Movies, Theaters, Tickets
├── Complex queries và analytics
└── Transactional operations

Realtime Database: Real-time features
├── Notifications, Seat reservations
├── Live updates và messaging
└── Simple key-value operations
```

### 6.2 Firestore Database Design

#### 6.2.1 Collection Structure

**Users Collection:**
```javascript
/users/{userId}
{
  id: "firebase_auth_uid",
  email: "<EMAIL>",
  name: "Nguyễn Văn A",
  photoUrl: "https://storage.googleapis.com/...",
  role: "user|admin|developer",
  phoneNumber: "+84901234567",
  dateOfBirth: "1990-01-01",
  gender: "male|female|other",
  preferences: {
    language: "vi|en",
    notifications: {
      booking: true,
      promotions: false,
      reminders: true
    },
    favoriteTheaters: ["theater1", "theater2"]
  },
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
  isActive: true
}
```

**Movies Collection:**
```javascript
/movies/{movieId}
{
  id: 123456,
  title: "Avengers: Endgame",
  originalTitle: "Avengers: Endgame",
  overview: "Mô tả phim...",
  posterPath: "/poster.jpg",
  backdropPath: "/backdrop.jpg",
  genres: ["Action", "Adventure", "Drama"],
  runtime: 181,
  releaseDate: "2019-04-26",
  language: "en",
  rating: 8.4,
  voteCount: 15000,
  status: "released|upcoming",
  trailerUrls: [
    "https://youtube.com/watch?v=...",
    "https://youtube.com/watch?v=..."
  ],
  cast: [
    {
      name: "Robert Downey Jr.",
      character: "Tony Stark / Iron Man",
      profilePath: "/actor.jpg"
    }
  ],
  crew: [
    {
      name: "Anthony Russo",
      job: "Director",
      profilePath: "/director.jpg"
    }
  ],
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z",
  isActive: true
}
```

**[CHÈN ẢNH: Firestore Console Screenshot - Hình 6.2]**
*Screenshot của Firestore console hiển thị collections và documents*

#### 6.2.2 Complex Collections

**Theaters Collection:**
```javascript
/theaters/{theaterId}
{
  id: "theater_001",
  name: "CGV Vincom Center",
  address: {
    street: "72 Lê Thánh Tôn",
    district: "Quận 1",
    city: "TP. Hồ Chí Minh",
    country: "Vietnam",
    postalCode: "70000",
    coordinates: {
      latitude: 10.7769,
      longitude: 106.7009
    }
  },
  phoneNumber: "+84283823456",
  email: "<EMAIL>",
  facilities: [
    "IMAX", "4DX", "VIP", "Parking", 
    "Food Court", "Wheelchair Access"
  ],
  operatingHours: {
    monday: { open: "09:00", close: "23:00" },
    tuesday: { open: "09:00", close: "23:00" },
    // ... other days
    sunday: { open: "09:00", close: "23:00" }
  },
  isActive: true,
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z"
}
```

**Screens Subcollection:**
```javascript
/theaters/{theaterId}/screens/{screenId}
{
  id: "screen_001",
  name: "Screen 1",
  type: "standard|imax|4dx|vip",
  totalSeats: 120,
  rows: 10,
  seatsPerRow: 12,
  seatLayout: {
    "A": [1,2,3,4,5,6,7,8,9,10,11,12],
    "B": [1,2,3,4,5,6,7,8,9,10,11,12],
    // ... other rows
    "J": [1,2,3,4,5,6,7,8,9,10,11,12]
  },
  seatTypes: {
    "A1-A12": "vip",
    "B1-B12": "vip", 
    "C1-J12": "standard"
  },
  amenities: [
    "Dolby Atmos", "Reclining Seats", 
    "Cup Holders", "USB Charging"
  ],
  isActive: true,
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-01T00:00:00Z"
}
```

#### 6.2.3 Transactional Collections

**Showtimes Collection:**
```javascript
/showtimes/{showtimeId}
{
  id: "showtime_001",
  movieId: 123456,
  theaterId: "theater_001",
  screenId: "screen_001",
  startTime: "2024-01-15T19:30:00Z",
  endTime: "2024-01-15T22:31:00Z",
  basePrice: 80000,
  pricing: {
    standard: 80000,
    vip: 120000,
    couple: 150000
  },
  bookedSeats: ["A1", "A2", "B5", "C10"],
  reservedSeats: [
    {
      seatId: "D5",
      userId: "user123",
      reservedAt: "2024-01-15T18:45:00Z",
      expiresAt: "2024-01-15T18:55:00Z"
    }
  ],
  availableSeats: 115,
  isActive: true,
  createdAt: "2024-01-01T00:00:00Z",
  updatedAt: "2024-01-15T18:45:00Z"
}
```

**Tickets Collection:**
```javascript
/tickets/{ticketId}
{
  id: "ticket_001",
  userId: "user123",
  showtimeId: "showtime_001",
  bookingCode: "DP240115001",
  selectedSeats: ["A1", "A2"],
  seatDetails: [
    {
      seatId: "A1",
      type: "vip",
      price: 120000
    },
    {
      seatId: "A2", 
      type: "vip",
      price: 120000
    }
  ],
  totalAmount: 240000,
  currency: "VND",
  status: "pending|confirmed|cancelled|expired|used",
  qrCode: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA...",
  paymentId: "payment_001",
  movieInfo: {
    title: "Avengers: Endgame",
    posterPath: "/poster.jpg",
    runtime: 181
  },
  theaterInfo: {
    name: "CGV Vincom Center",
    address: "72 Lê Thánh Tôn, Q1, TP.HCM"
  },
  bookedAt: "2024-01-15T18:50:00Z",
  expiresAt: "2024-01-15T19:30:00Z",
  usedAt: null,
  createdAt: "2024-01-15T18:50:00Z",
  updatedAt: "2024-01-15T18:50:00Z"
}
```

**[CHÈN ẢNH: Ticket QR Code Example - Hình 6.3]**
*Ví dụ về QR code được generate cho vé điện tử*

### 6.3 Realtime Database Design

#### 6.3.1 Real-time Collections Structure

**Notifications Tree:**
```json
{
  "notifications": {
    "user123": {
      "notification_001": {
        "id": "notification_001",
        "type": "booking_confirmation",
        "title": "Đặt vé thành công",
        "message": "Bạn đã đặt vé xem phim Avengers: Endgame thành công",
        "data": {
          "ticketId": "ticket_001",
          "movieTitle": "Avengers: Endgame",
          "showtime": "2024-01-15T19:30:00Z"
        },
        "isRead": false,
        "isDeleted": false,
        "createdAt": "2024-01-15T18:50:00Z",
        "readAt": null
      }
    }
  }
}
```

**Seat Reservations Tree:**
```json
{
  "seat_reservations": {
    "showtime_001": {
      "A1": {
        "userId": "user123",
        "reservedAt": "2024-01-15T18:45:00Z",
        "expiresAt": "2024-01-15T18:55:00Z",
        "status": "reserved"
      },
      "A2": {
        "userId": "user123", 
        "reservedAt": "2024-01-15T18:45:00Z",
        "expiresAt": "2024-01-15T18:55:00Z",
        "status": "reserved"
      }
    }
  }
}
```

**Bug Reports Tree:**
```json
{
  "bug_reports": {
    "report_001": {
      "id": "report_001",
      "userId": "user123",
      "title": "Lỗi thanh toán PayPal",
      "description": "Không thể hoàn thành thanh toán...",
      "severity": "high",
      "status": "open",
      "category": "payment",
      "screenshots": [
        "https://storage.googleapis.com/screenshot1.png"
      ],
      "deviceInfo": {
        "platform": "android",
        "version": "13",
        "model": "Samsung Galaxy S23",
        "appVersion": "1.0.0"
      },
      "assignedTo": "developer123",
      "reportedAt": "2024-01-15T18:30:00Z",
      "updatedAt": "2024-01-15T18:30:00Z",
      "resolvedAt": null
    }
  }
}
```

### 6.4 Database Indexes và Optimization

#### 6.4.1 Firestore Composite Indexes

**Movie Search Indexes:**
```json
{
  "indexes": [
    {
      "collectionGroup": "movies",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "status", "order": "ASCENDING"},
        {"fieldPath": "releaseDate", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "movies", 
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "genres", "arrayConfig": "CONTAINS"},
        {"fieldPath": "rating", "order": "DESCENDING"}
      ]
    }
  ]
}
```

**Booking Query Indexes:**
```json
{
  "indexes": [
    {
      "collectionGroup": "tickets",
      "queryScope": "COLLECTION", 
      "fields": [
        {"fieldPath": "userId", "order": "ASCENDING"},
        {"fieldPath": "status", "order": "ASCENDING"},
        {"fieldPath": "bookedAt", "order": "DESCENDING"}
      ]
    },
    {
      "collectionGroup": "showtimes",
      "queryScope": "COLLECTION",
      "fields": [
        {"fieldPath": "movieId", "order": "ASCENDING"},
        {"fieldPath": "startTime", "order": "ASCENDING"}
      ]
    }
  ]
}
```

**[CHÈN ẢNH: Firestore Indexes Console - Hình 6.4]**
*Screenshot của Firestore indexes configuration*

#### 6.4.2 Query Optimization Strategies

**Efficient Query Patterns:**
```dart
// Good: Use indexes effectively
Query moviesQuery = FirebaseFirestore.instance
    .collection('movies')
    .where('status', isEqualTo: 'released')
    .orderBy('releaseDate', descending: true)
    .limit(20);

// Good: Paginated queries
Query paginatedQuery = FirebaseFirestore.instance
    .collection('movies')
    .orderBy('title')
    .startAfterDocument(lastDocument)
    .limit(10);

// Avoid: Large result sets without limits
// Query badQuery = FirebaseFirestore.instance
//     .collection('movies'); // Returns all movies
```

**Caching Strategy:**
```dart
// Enable offline persistence
FirebaseFirestore.instance.settings = Settings(
  persistenceEnabled: true,
  cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED,
);

// Use cache-first queries for static data
Query cachedQuery = FirebaseFirestore.instance
    .collection('movies')
    .where('id', isEqualTo: movieId)
    .get(GetOptions(source: Source.cache));
```

### 6.5 Data Security và Privacy

#### 6.5.1 Firestore Security Rules

**User Data Protection:**
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null 
                        && request.auth.uid == userId;
      allow read: if request.auth != null 
                  && isAdmin();
    }
    
    // Movies are public read, admin write
    match /movies/{movieId} {
      allow read: if true;
      allow write: if isAdmin();
    }
    
    // Tickets belong to users
    match /tickets/{ticketId} {
      allow read, write: if request.auth != null 
                        && resource.data.userId == request.auth.uid;
      allow read: if isAdmin();
    }
    
    // Helper functions
    function isAdmin() {
      return request.auth != null 
             && request.auth.token.role in ['admin', 'developer'];
    }
    
    function isOwner(userId) {
      return request.auth != null 
             && request.auth.uid == userId;
    }
  }
}
```

**[CHÈN ẢNH: Security Rules Testing - Hình 6.5]**
*Screenshot của Firebase Security Rules testing interface*

#### 6.5.2 Realtime Database Security Rules

```json
{
  "rules": {
    "notifications": {
      "$userId": {
        ".read": "$userId === auth.uid || isAdmin()",
        ".write": "$userId === auth.uid || isAdmin()",
        ".validate": "newData.hasChildren(['id', 'type', 'title', 'message'])"
      }
    },
    "seat_reservations": {
      "$showtimeId": {
        "$seatId": {
          ".read": true,
          ".write": "auth != null && (
            !data.exists() || 
            data.child('userId').val() === auth.uid ||
            isAdmin()
          )"
        }
      }
    },
    "bug_reports": {
      "$reportId": {
        ".read": "auth != null && (
          data.child('userId').val() === auth.uid ||
          isAdmin()
        )",
        ".write": "auth != null"
      }
    }
  }
}
```

### 6.6 Backup và Recovery Strategy

#### 6.6.1 Automated Backup

**Firestore Backup:**
```javascript
// Cloud Function for daily backup
exports.dailyBackup = functions.pubsub
  .schedule('0 2 * * *') // Daily at 2 AM
  .timeZone('Asia/Ho_Chi_Minh')
  .onRun(async (context) => {
    const client = new FirestoreAdminClient();
    const projectId = process.env.GCP_PROJECT;
    const databaseName = client.databasePath(projectId, '(default)');
    
    return client.exportDocuments({
      name: databaseName,
      outputUriPrefix: `gs://${projectId}-backups/${new Date().toISOString()}`,
      collectionIds: ['users', 'movies', 'theaters', 'tickets', 'payments']
    });
  });
```

**Recovery Procedures:**
1. **Point-in-time Recovery:** Restore từ specific backup timestamp
2. **Selective Recovery:** Restore specific collections
3. **Data Validation:** Verify data integrity sau recovery
4. **Rollback Plan:** Revert changes nếu có issues

#### 6.6.2 Data Migration Strategy

**Version Control:**
```dart
class DatabaseMigration {
  static const int CURRENT_VERSION = 3;
  
  static Future<void> migrate() async {
    int currentVersion = await getCurrentVersion();
    
    for (int version = currentVersion + 1; 
         version <= CURRENT_VERSION; 
         version++) {
      await runMigration(version);
    }
  }
  
  static Future<void> runMigration(int version) async {
    switch (version) {
      case 2:
        await addUserPreferences();
        break;
      case 3:
        await addSeatTypes();
        break;
    }
  }
}
```

**[CHÈN ẢNH: Database Migration Dashboard - Hình 6.6]**
*Dashboard hiển thị trạng thái migration và backup*

---

*Phần tiếp theo: 7. Thiết kế API và Services*
