import 'package:flutter/material.dart';

/// Centralized color management for the Đớp Phim app
/// Modern neutral theme với màu sắc trung tính và dễ nhìn
/// Sử dụng xám xanh làm chủ đạo với accent xanh mint nhẹ nhàng
class AppColors {
  // Private constructor to prevent instantiation
  AppColors._();

  // Primary gradient colors - Neutral modern theme
  static const Color primaryGradientStart = Color(0xFF475569); // Xám xanh đậm
  static const Color primaryGradientEnd =
      Color(0xFF64748B); // Xám xanh trung bình
  static const Color primaryGradientAccent = Color(0xFF10B981); // Xanh mint

  // Background colors - Soft dark theme
  static const Color scaffoldBackground = Color(0xFF0F172A); // Nền tối mềm
  static const Color cardBackground = Color(0xFF1E293B); // Card xám xanh
  static const Color surfaceColor = Color(0xFF334155); // Surface xám xanh

  // Primary colors - Neutral palette
  static const Color primarySlate = Color(0xFF475569); // Xám xanh chính
  static const Color primaryMint = Color(0xFF10B981); // Xanh mint accent
  static const Color primaryWhite = Color(0xFFF8FAFC); // Trắng mềm

  // Accent colors - Soft tones
  static const Color accentBlue = Color(0xFF3B82F6); // Xanh dương mềm
  static const Color accentMint = Color(0xFF34D399); // Xanh mint sáng
  static const Color accentWarmGray = Color(0xFF6B7280); // Xám ấm

  // Status colors - Soft status colors
  static const Color successGreen = Color(0xFF10B981); // Xanh mint thành công
  static const Color warningOrange = Color(0xFFF59E0B); // Cam amber mềm
  static const Color errorRed = Color(0xFFEF4444); // Đỏ mềm
  static const Color infoBlue = Color(0xFF3B82F6); // Xanh dương thông tin

  // Text colors - Neutral text hierarchy
  static const Color textPrimary = Color(0xFFF8FAFC); // Text chính trắng mềm
  static const Color textSecondary = Color(0xFFCBD5E1); // Text phụ xám sáng
  static const Color textTertiary = Color(0xFF94A3B8); // Text tertiary xám
  static const Color textDisabled = Color(0xFF64748B); // Text disabled xám đậm
  static const Color textOnDark = Color(0xFFF8FAFC); // Text trên nền tối
  static const Color textOnLight = Color(0xFF1E293B); // Text trên nền sáng

  // Button colors - Neutral button colors
  static const Color buttonPrimary = Color(0xFF475569); // Xám xanh chính
  static const Color buttonSecondary = Color(0xFF64748B); // Xám xanh sáng
  static const Color buttonDanger = Color(0xFFEF4444); // Đỏ nguy hiểm mềm
  static const Color buttonSuccess = Color(0xFF10B981); // Xanh mint thành công
  static const Color buttonWarning = Color(0xFFF59E0B); // Cam amber
  static const Color buttonAccent = Color(0xFF3B82F6); // Xanh dương accent

  // Border colors - Neutral outline colors
  static const Color borderPrimary = Color(0xFF475569); // Border chính xám xanh
  static const Color borderSecondary = Color(0xFF64748B); // Border phụ
  static const Color borderAccent =
      Color(0xFF10B981); // Xanh mint cho border accent

  // Overlay colors - Điều chỉnh cho cinema theme
  static const Color overlayLight = Color(0x1AFFFFFF);
  static const Color overlayMedium = Color(0x33FFFFFF);
  static const Color overlayDark = Color(0x66000000);
  static const Color overlayRed = Color(0x33DC143C); // Overlay đỏ cinema

  // Chat/Message colors - Cinema theme cho bug reports và notifications
  static const Color chatSystemBackground =
      Color(0xFF8B0000); // Đỏ burgundy cho system
  static const Color chatDeveloperBackground =
      Color(0xFFDC143C); // Đỏ cinema cho developer
  static const Color chatAdminBackground = Color(0xFF4CAF50); // Xanh cho admin
  static const Color chatUserBackground = Color(0xFF3C3C3C); // Xám ấm cho user
  static const Color chatCurrentUserBackground =
      Color(0xFF800020); // Đỏ đậm cho current user

  // Screen type colors - Cinema theme
  static const Color screenStandard =
      Color(0xFFDC143C); // Đỏ cinema cho standard
  static const Color screenVip = Color(0xFFFFD700); // Vàng gold cho VIP
  static const Color screenImax = Color(0xFF8B0000); // Đỏ burgundy cho IMAX
  static const Color screenDolby = Color(0xFF4CAF50); // Xanh cho Dolby
  static const Color screenPremium = Color(0xFFFF8C00); // Cam cho Premium

  // Seat colors - Tối ưu cho cinema theme
  static const Color seatAvailable =
      Color(0x4DFFFFFF); // Trắng mờ cho ghế trống
  static const Color seatSelected =
      Color(0xFFDC143C); // Đỏ cinema cho ghế đã chọn
  static const Color seatBooked = Color(0xFF757575); // Xám cho ghế đã đặt
  static const Color seatVip = Color(0xFFFFD700); // Vàng gold cho ghế VIP
  static const Color seatCouple = Color(0xFFFF69B4); // Hồng cho ghế đôi
  static const Color seatDisabled =
      Color(0xFF424242); // Xám đậm cho ghế disabled

  // Genre selection colors - Cinema theme
  static const Color genreSelected =
      Color(0xFFFFD700); // Vàng gold cho genre đã chọn
  static const Color genreUnselected =
      Color(0x1EDC143C); // Đỏ mờ cho genre chưa chọn

  // Status chip colors - Cinema theme
  static const Color statusPending = Color(0xFF9E9E9E); // Xám cho pending
  static const Color statusAccepted =
      Color(0xFFDC143C); // Đỏ cinema cho accepted
  static const Color statusInProgress =
      Color(0xFFFF8C00); // Cam cho in progress
  static const Color statusFixed = Color(0xFF4CAF50); // Xanh cho fixed

  // Backward compatibility - Giữ tên cũ để không phá vỡ code hiện tại
  static const Color primaryBlue =
      primarySlate; // Thay primaryBlue bằng primarySlate
  static const Color primaryAmber =
      primaryMint; // Thay primaryAmber bằng primaryMint

  // Common gradients - Cinema Modern theme
  static const LinearGradient primaryGradient = LinearGradient(
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
    colors: [primaryGradientStart, primaryGradientEnd, primaryGradientAccent],
    stops: [0.0, 0.5, 1.0],
  );

  static const LinearGradient primaryGradientVertical = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [primaryGradientStart, primaryGradientEnd, primaryGradientAccent],
    stops: [0.0, 0.5, 1.0],
  );

  // Button gradient - Cinema theme
  static const LinearGradient buttonGradient = LinearGradient(
    colors: [primaryGradientStart, primaryGradientEnd],
  );

  // Cinema overlay gradient
  static const LinearGradient overlayGradient = LinearGradient(
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
    colors: [
      Colors.transparent,
      Color(0x4D8B0000), // Đỏ burgundy mờ
      Color(0x4DDC143C), // Đỏ cinema mờ
      Color(0xB3800020), // Đỏ đậm
    ],
  );

  // Genre gradients - Cinema theme
  static const LinearGradient genreSelectedGradient = LinearGradient(
    colors: [Color(0xFFFFD700), Color(0xFFFFB74D)], // Vàng gold gradient
  );

  static const LinearGradient genreUnselectedGradient = LinearGradient(
    colors: [Color(0x1EDC143C), Color(0x1E8B0000)], // Đỏ mờ gradient
  );

  // Helper methods
  static Color withOpacity(Color color, double opacity) {
    return color.withOpacity(opacity);
  }

  static Color getChatBackgroundColor({
    required String responderId,
    required bool isFromDeveloper,
    required bool isFromAdmin,
    required bool isCurrentUser,
  }) {
    if (responderId == 'system') {
      return chatSystemBackground.withOpacity(0.3);
    } else if (isFromDeveloper) {
      return chatDeveloperBackground.withOpacity(0.8);
    } else if (isFromAdmin) {
      return chatAdminBackground.withOpacity(0.8);
    } else {
      return isCurrentUser
          ? chatCurrentUserBackground.withOpacity(0.6)
          : chatUserBackground.withOpacity(0.4);
    }
  }

  static Color getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return statusPending;
      case 'accepted':
        return statusAccepted;
      case 'inprogress':
      case 'in_progress':
        return statusInProgress;
      case 'fixed':
        return statusFixed;
      default:
        return statusPending;
    }
  }

  static Color getScreenTypeColor(String screenType) {
    switch (screenType.toLowerCase()) {
      case 'standard':
        return screenStandard;
      case 'vip':
        return screenVip;
      case 'imax':
        return screenImax;
      case 'dolby':
        return screenDolby;
      case 'premium':
        return screenPremium;
      default:
        return screenStandard;
    }
  }
}
