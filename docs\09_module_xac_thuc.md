# PHẦN III: TRIỂN KHAI CHI TIẾT

## 9. MODULE XÁC THỰC VÀ PHÂN QUYỀN

### 9.1 Tổng quan module Authentication

Module xác thực là nền tảng bảo mật của hệ thống "Đớ<PERSON> Phim", đ<PERSON><PERSON> bảo chỉ những người dùng hợp lệ mới có thể truy cập và sử dụng các tính năng của ứng dụng. Module được xây dựng trên Firebase Authentication với tích hợp Google Sign-In và hệ thống phân quyền 3 cấp độ.

**[CHÈN ẢNH: Authentication Flow Diagram - Hình 9.1]**
*S<PERSON> đồ luồng xác thực từ đăng ký đến đăng nhập thành công*

#### 9.1.1 Kiến trúc Authentication

**Authentication Stack:**
```
User Interface (Login/Register Screens)
        ↓
Auth Controller (GetX State Management)
        ↓
Auth Service (Business Logic)
        ↓
Firebase Auth Provider
        ↓
User Repository (Firestore)
```

**Security Layers:**
1. **Client-side Validation:** Input validation và UI feedback
2. **Firebase Auth:** Server-side authentication và session management
3. **Custom Claims:** Role-based access control
4. **Firestore Rules:** Database-level security
5. **App Check:** Bot protection và API security

### 9.2 Firebase Authentication Integration

#### 9.2.1 Firebase Configuration

**Firebase Auth Setup:**
```dart
// lib/core/config/firebase_config.dart
class FirebaseConfig {
  static Future<void> initialize() async {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    
    // Configure Auth settings
    await FirebaseAuth.instance.setSettings(
      appVerificationDisabledForTesting: false,
      forceRecaptchaFlow: true,
    );
    
    // Enable App Check
    await FirebaseAppCheck.instance.activate(
      webRecaptchaSiteKey: 'your-recaptcha-site-key',
      androidProvider: AndroidProvider.debug,
      appleProvider: AppleProvider.debug,
    );
  }
}
```

**Auth State Management:**
```dart
// lib/services/auth_service.dart
class AuthService extends GetxService {
  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final GoogleSignIn _googleSignIn = GoogleSignIn();

  // Reactive user state
  Rx<User?> firebaseUser = Rx<User?>(null);
  Rx<UserModel?> currentUser = Rx<UserModel?>(null);

  @override
  void onInit() {
    super.onInit();
    // Listen to auth state changes
    firebaseUser.bindStream(_auth.authStateChanges());
    ever(firebaseUser, _setInitialScreen);
  }

  void _setInitialScreen(User? user) async {
    if (user == null) {
      // User is logged out
      currentUser.value = null;
      Get.offAllNamed(Routes.LOGIN);
    } else {
      // User is logged in, fetch user data
      await _loadUserData(user.uid);
      Get.offAllNamed(Routes.HOME);
    }
  }

  Future<void> _loadUserData(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        currentUser.value = UserModel.fromFirestore(doc);
      }
    } catch (e) {
      print('Error loading user data: $e');
    }
  }
}
```

#### 9.2.2 User Registration Implementation

**Registration Implementation:**

Quy trình đăng ký được thiết kế với 5 bước chính:

1. **Input Validation:** Kiểm tra tính hợp lệ của email, mật khẩu, tên người dùng
2. **Firebase Authentication:** Tạo tài khoản với Firebase Auth service
3. **Profile Creation:** Tạo document người dùng trong Firestore
4. **Email Verification:** Gửi email xác thực tự động
5. **UI Feedback:** Hiển thị thông báo và chuyển hướng người dùng

**Validation Rules:**
- Email: Định dạng hợp lệ, không trùng lặp
- Mật khẩu: Tối thiểu 6 ký tự, có chữ và số
- Tên: 2-50 ký tự, không chứa từ cấm
- Số điện thoại: Định dạng Việt Nam (tùy chọn)

**Error Handling Strategy:**
- Client-side validation cho UX tốt
- Server-side validation cho security
- Localized error messages bằng tiếng Việt
- Graceful fallback cho network errors

**Security Measures:**
- Password hashing tự động bởi Firebase
- Email verification bắt buộc
- Rate limiting cho registration attempts
- Input sanitization chống XSS attacks
```

**[CHÈN ẢNH: Registration Screen - Hình 9.2]**
*Screenshot của màn hình đăng ký với form validation*

#### 9.2.3 Login Implementation

**Email/Password Login:**
```dart
Future<void> login() async {
  if (!formKey.currentState!.validate()) return;

  isLoading.value = true;
  
  try {
    final credential = await FirebaseAuth.instance.signInWithEmailAndPassword(
      email: emailController.text.trim(),
      password: passwordController.text,
    );

    if (credential.user != null) {
      if (!credential.user!.emailVerified) {
        await FirebaseAuth.instance.signOut();
        Get.snackbar(
          'Email chưa xác thực',
          'Vui lòng xác thực email trước khi đăng nhập.',
          backgroundColor: AppColors.warning,
          colorText: Colors.white,
        );
        Get.toNamed(Routes.EMAIL_VERIFICATION);
        return;
      }

      // Update last login time
      await _updateLastLogin(credential.user!.uid);
      
      Get.snackbar(
        'Đăng nhập thành công',
        'Chào mừng bạn trở lại!',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
      );
    }
  } on FirebaseAuthException catch (e) {
    _handleLoginError(e);
  } catch (e) {
    Get.snackbar('Lỗi', 'Đã xảy ra lỗi không xác định: $e');
  } finally {
    isLoading.value = false;
  }
}

Future<void> _updateLastLogin(String uid) async {
  await FirebaseFirestore.instance
      .collection('users')
      .doc(uid)
      .update({
    'lastLoginAt': FieldValue.serverTimestamp(),
    'updatedAt': FieldValue.serverTimestamp(),
  });
}

void _handleLoginError(FirebaseAuthException e) {
  String message;
  switch (e.code) {
    case 'user-not-found':
      message = 'Không tìm thấy tài khoản với email này.';
      break;
    case 'wrong-password':
      message = 'Mật khẩu không chính xác.';
      break;
    case 'invalid-email':
      message = 'Email không hợp lệ.';
      break;
    case 'user-disabled':
      message = 'Tài khoản này đã bị vô hiệu hóa.';
      break;
    case 'too-many-requests':
      message = 'Quá nhiều lần thử đăng nhập. Vui lòng thử lại sau.';
      break;
    default:
      message = 'Đăng nhập thất bại: ${e.message}';
  }
  
  Get.snackbar(
    'Lỗi đăng nhập',
    message,
    backgroundColor: AppColors.error,
    colorText: Colors.white,
  );
}
```

**Google Sign-In Integration:**
```dart
Future<void> signInWithGoogle() async {
  isLoading.value = true;
  
  try {
    // Trigger the authentication flow
    final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();
    
    if (googleUser == null) {
      // User cancelled the sign-in
      isLoading.value = false;
      return;
    }

    // Obtain the auth details from the request
    final GoogleSignInAuthentication googleAuth = await googleUser.authentication;

    // Create a new credential
    final credential = GoogleAuthProvider.credential(
      accessToken: googleAuth.accessToken,
      idToken: googleAuth.idToken,
    );

    // Sign in to Firebase with the Google credential
    final userCredential = await FirebaseAuth.instance.signInWithCredential(credential);
    
    if (userCredential.user != null) {
      // Check if user exists in Firestore
      final userDoc = await FirebaseFirestore.instance
          .collection('users')
          .doc(userCredential.user!.uid)
          .get();
      
      if (!userDoc.exists) {
        // Create new user profile for Google sign-in
        await _createGoogleUserProfile(userCredential.user!);
      }
      
      Get.snackbar(
        'Đăng nhập thành công',
        'Chào mừng bạn đến với Đớp Phim!',
        backgroundColor: AppColors.success,
        colorText: Colors.white,
      );
    }
  } catch (e) {
    Get.snackbar(
      'Lỗi đăng nhập Google',
      'Không thể đăng nhập bằng Google: $e',
      backgroundColor: AppColors.error,
      colorText: Colors.white,
    );
  } finally {
    isLoading.value = false;
  }
}

Future<void> _createGoogleUserProfile(User firebaseUser) async {
  final userModel = UserModel(
    id: firebaseUser.uid,
    email: firebaseUser.email!,
    name: firebaseUser.displayName ?? 'Người dùng Google',
    photoUrl: firebaseUser.photoURL,
    role: UserRole.user,
    preferences: UserPreferences.defaultPreferences(),
    createdAt: DateTime.now(),
    updatedAt: DateTime.now(),
    isActive: true,
    authProvider: 'google',
  );

  await FirebaseFirestore.instance
      .collection('users')
      .doc(firebaseUser.uid)
      .set(userModel.toFirestore());
}
```

**[CHÈN ẢNH: Login Screen with Google - Hình 9.3]**
*Màn hình đăng nhập với tùy chọn Google Sign-In*

### 9.3 Role-Based Access Control (RBAC)

#### 9.3.1 User Roles Definition

**User Role Enum:**
```dart
// lib/models/user_role.dart
enum UserRole {
  user('user', 'Người dùng'),
  admin('admin', 'Quản trị viên'),
  developer('developer', 'Nhà phát triển');

  const UserRole(this.value, this.displayName);
  
  final String value;
  final String displayName;

  static UserRole fromString(String value) {
    return UserRole.values.firstWhere(
      (role) => role.value == value,
      orElse: () => UserRole.user,
    );
  }

  // Permission checks
  bool get canAccessAdminPanel => this == admin || this == developer;
  bool get canManageUsers => this == admin || this == developer;
  bool get canManageMovies => this == admin || this == developer;
  bool get canManageTheaters => this == admin || this == developer;
  bool get canViewReports => this == admin || this == developer;
  bool get canDebugSystem => this == developer;
  bool get canModifyRoles => this == developer;
}
```

**Permission Service:**
```dart
// lib/services/permission_service.dart
class PermissionService extends GetxService {
  final AuthService _authService = Get.find();

  UserRole get currentUserRole {
    return _authService.currentUser.value?.role ?? UserRole.user;
  }

  bool hasPermission(String permission) {
    final role = currentUserRole;
    
    switch (permission) {
      case 'admin_access':
        return role.canAccessAdminPanel;
      case 'manage_users':
        return role.canManageUsers;
      case 'manage_movies':
        return role.canManageMovies;
      case 'manage_theaters':
        return role.canManageTheaters;
      case 'view_reports':
        return role.canViewReports;
      case 'debug_system':
        return role.canDebugSystem;
      case 'modify_roles':
        return role.canModifyRoles;
      default:
        return false;
    }
  }

  bool canAccessRoute(String route) {
    switch (route) {
      case Routes.ADMIN_DASHBOARD:
      case Routes.MANAGE_MOVIES:
      case Routes.MANAGE_THEATERS:
      case Routes.MANAGE_USERS:
        return hasPermission('admin_access');
      case Routes.SYSTEM_DEBUG:
        return hasPermission('debug_system');
      default:
        return true; // Public routes
    }
  }
}
```

#### 9.3.2 Admin Access Implementation

**Hidden Admin Access (7 taps):**
```dart
// lib/view/page/profile/profile_page.dart
class ProfilePage extends StatefulWidget {
  @override
  _ProfilePageState createState() => _ProfilePageState();
}

class _ProfilePageState extends State<ProfilePage> {
  int _tapCount = 0;
  Timer? _resetTimer;

  void _onUsernameTap() {
    setState(() {
      _tapCount++;
    });

    // Reset counter after 3 seconds of inactivity
    _resetTimer?.cancel();
    _resetTimer = Timer(Duration(seconds: 3), () {
      setState(() {
        _tapCount = 0;
      });
    });

    if (_tapCount >= 7) {
      _showAdminLogin();
      setState(() {
        _tapCount = 0;
      });
    }
  }

  void _showAdminLogin() {
    Get.dialog(
      AdminLoginDialog(),
      barrierDismissible: false,
    );
  }

  @override
  Widget build(BuildContext context) {
    final authController = Get.find<AuthController>();
    
    return Scaffold(
      body: SafeArea(
        child: Column(
          children: [
            // User info section
            Container(
              padding: EdgeInsets.all(24),
              child: Column(
                children: [
                  GestureDetector(
                    onTap: _onUsernameTap,
                    child: Obx(() => Column(
                      children: [
                        CircleAvatar(
                          radius: 50,
                          backgroundImage: NetworkImage(
                            authController.currentUser.value?.photoUrl ?? '',
                          ),
                        ),
                        SizedBox(height: 16),
                        Text(
                          authController.currentUser.value?.name ?? '',
                          style: AppTypography.headlineSmall,
                        ),
                        Text(
                          authController.currentUser.value?.email ?? '',
                          style: AppTypography.bodyMedium.copyWith(
                            color: Colors.grey[600],
                          ),
                        ),
                        if (_tapCount > 0 && _tapCount < 7)
                          Text(
                            'Còn ${7 - _tapCount} lần nữa...',
                            style: TextStyle(
                              color: Colors.grey[400],
                              fontSize: 12,
                            ),
                          ),
                      ],
                    )),
                  ),
                ],
              ),
            ),
            // ... rest of profile UI
          ],
        ),
      ),
    );
  }
}
```

**Admin Login Dialog:**
```dart
// lib/view/widgets/dialogs/admin_login_dialog.dart
class AdminLoginDialog extends StatelessWidget {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _isLoading = false.obs;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Row(
        children: [
          Icon(Icons.admin_panel_settings, color: AppColors.primary),
          SizedBox(width: 8),
          Text('Đăng nhập Admin'),
        ],
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          TextField(
            controller: _emailController,
            decoration: InputDecoration(
              labelText: 'Email Admin',
              prefixIcon: Icon(Icons.email),
            ),
            keyboardType: TextInputType.emailAddress,
          ),
          SizedBox(height: 16),
          TextField(
            controller: _passwordController,
            decoration: InputDecoration(
              labelText: 'Mật khẩu Admin',
              prefixIcon: Icon(Icons.lock),
            ),
            obscureText: true,
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Get.back(),
          child: Text('Hủy'),
        ),
        Obx(() => ElevatedButton(
          onPressed: _isLoading.value ? null : _verifyAdminCredentials,
          child: _isLoading.value
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Text('Đăng nhập'),
        )),
      ],
    );
  }

  Future<void> _verifyAdminCredentials() async {
    _isLoading.value = true;
    
    try {
      // Verify admin credentials
      final result = await FirebaseFunctions.instance
          .httpsCallable('verifyAdminAccess')
          .call({
        'email': _emailController.text.trim(),
        'password': _passwordController.text,
      });

      if (result.data['success']) {
        Get.back(); // Close dialog
        Get.toNamed(Routes.ADMIN_DASHBOARD);
        Get.snackbar(
          'Thành công',
          'Chào mừng đến Admin Panel',
          backgroundColor: AppColors.success,
          colorText: Colors.white,
        );
      } else {
        Get.snackbar(
          'Lỗi',
          'Thông tin đăng nhập không chính xác',
          backgroundColor: AppColors.error,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Lỗi',
        'Không thể xác thực: $e',
        backgroundColor: AppColors.error,
        colorText: Colors.white,
      );
    } finally {
      _isLoading.value = false;
    }
  }
}
```

**[CHÈN ẢNH: Admin Login Dialog - Hình 9.4]**
*Dialog đăng nhập admin xuất hiện sau 7 lần tap*

### 9.4 Security Implementation

#### 9.4.1 Input Validation

**Validation Rules:**
```dart
// lib/utils/validators.dart
class Validators {
  static String? email(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email không được để trống';
    }
    
    final emailRegex = RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
    if (!emailRegex.hasMatch(value)) {
      return 'Email không hợp lệ';
    }
    
    return null;
  }

  static String? password(String? value) {
    if (value == null || value.isEmpty) {
      return 'Mật khẩu không được để trống';
    }
    
    if (value.length < 6) {
      return 'Mật khẩu phải có ít nhất 6 ký tự';
    }
    
    // Check for at least one letter and one number
    if (!RegExp(r'^(?=.*[A-Za-z])(?=.*\d)').hasMatch(value)) {
      return 'Mật khẩu phải có ít nhất 1 chữ cái và 1 số';
    }
    
    return null;
  }

  static String? name(String? value) {
    if (value == null || value.isEmpty) {
      return 'Tên không được để trống';
    }
    
    if (value.length < 2) {
      return 'Tên phải có ít nhất 2 ký tự';
    }
    
    if (value.length > 50) {
      return 'Tên không được quá 50 ký tự';
    }
    
    // Check for inappropriate content
    final inappropriateWords = ['admin', 'test', 'null', 'undefined'];
    if (inappropriateWords.any((word) => value.toLowerCase().contains(word))) {
      return 'Tên chứa từ không được phép';
    }
    
    return null;
  }

  static String? phoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Phone is optional
    }
    
    // Vietnamese phone number format
    final phoneRegex = RegExp(r'^(\+84|0)[3|5|7|8|9][0-9]{8}$');
    if (!phoneRegex.hasMatch(value)) {
      return 'Số điện thoại không hợp lệ';
    }
    
    return null;
  }
}
```

#### 9.4.2 Session Management

**Auto Logout Implementation:**
```dart
// lib/services/session_service.dart
class SessionService extends GetxService {
  Timer? _sessionTimer;
  static const int SESSION_TIMEOUT_MINUTES = 30;

  void startSessionTimer() {
    _resetSessionTimer();
  }

  void _resetSessionTimer() {
    _sessionTimer?.cancel();
    _sessionTimer = Timer(
      Duration(minutes: SESSION_TIMEOUT_MINUTES),
      _handleSessionTimeout,
    );
  }

  void _handleSessionTimeout() {
    Get.dialog(
      AlertDialog(
        title: Text('Phiên đăng nhập hết hạn'),
        content: Text('Vui lòng đăng nhập lại để tiếp tục sử dụng.'),
        actions: [
          ElevatedButton(
            onPressed: () {
              Get.back();
              _logout();
            },
            child: Text('Đăng nhập lại'),
          ),
        ],
      ),
      barrierDismissible: false,
    );
  }

  Future<void> _logout() async {
    await FirebaseAuth.instance.signOut();
    await GoogleSignIn().signOut();
    Get.offAllNamed(Routes.LOGIN);
  }

  void extendSession() {
    _resetSessionTimer();
  }

  @override
  void onClose() {
    _sessionTimer?.cancel();
    super.onClose();
  }
}
```

**[CHÈN ẢNH: Security Features Overview - Hình 9.5]**
*Tổng quan các tính năng bảo mật được implement*

---

*Phần tiếp theo: 10. Module quản lý phim*
