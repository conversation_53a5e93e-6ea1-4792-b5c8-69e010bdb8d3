import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:media_kit/media_kit.dart';
import 'package:media_kit_video/media_kit_video.dart';
import 'package:get/get.dart';

/// Widget phát video fullscreen với controls tùy chỉnh
class FullscreenVideoPlayer extends StatefulWidget {
  final Player player;
  final VideoController videoController;
  final String movieTitle;

  const FullscreenVideoPlayer({
    Key? key,
    required this.player,
    required this.videoController,
    required this.movieTitle,
  }) : super(key: key);

  @override
  State<FullscreenVideoPlayer> createState() => _FullscreenVideoPlayerState();
}

class _FullscreenVideoPlayerState extends State<FullscreenVideoPlayer> {
  bool _showControls = true;
  bool _isPlaying = false;

  // Video controls state
  Duration _duration = Duration.zero;
  Duration _position = Duration.zero;
  double _volume = 1.0;
  double _playbackSpeed = 1.0;
  bool _isDragging = false;

  // Stream subscriptions
  late StreamSubscription<bool> _playingSubscription;
  late StreamSubscription<Duration> _durationSubscription;
  late StreamSubscription<Duration> _positionSubscription;
  late StreamSubscription<double> _volumeSubscription;

  @override
  void initState() {
    super.initState();

    print('FullscreenPlayer: Initializing fullscreen mode');
    print(
        'FullscreenPlayer: Player state - Duration: ${widget.player.state.duration}, Position: ${widget.player.state.position}');

    // Thiết lập fullscreen mode
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);

    // Khởi tạo state từ player hiện tại
    _isPlaying = widget.player.state.playing;
    _duration = widget.player.state.duration;
    _position = widget.player.state.position;
    _volume = widget.player.state.volume / 100.0;

    print(
        'FullscreenPlayer: Initial state - Playing: $_isPlaying, Duration: $_duration, Position: $_position');

    // Lắng nghe trạng thái player với StreamSubscription
    _playingSubscription = widget.player.stream.playing.listen((playing) {
      if (mounted) {
        print('FullscreenPlayer: Playing changed: $playing');
        setState(() {
          _isPlaying = playing;
        });
      }
    });

    // Lắng nghe duration
    _durationSubscription = widget.player.stream.duration.listen((duration) {
      if (mounted) {
        print('FullscreenPlayer: Duration updated: $duration');
        setState(() {
          _duration = duration;
        });
      }
    });

    // Lắng nghe position
    _positionSubscription = widget.player.stream.position.listen((position) {
      if (mounted && !_isDragging) {
        setState(() {
          _position = position;
        });
      }
    });

    // Lắng nghe volume
    _volumeSubscription = widget.player.stream.volume.listen((volume) {
      if (mounted) {
        setState(() {
          _volume = volume / 100.0; // Media kit volume is 0-100
        });
      }
    });

    // Ẩn controls sau 3 giây
    _hideControlsAfterDelay();
  }

  @override
  void dispose() {
    // Cancel stream subscriptions
    _playingSubscription.cancel();
    _durationSubscription.cancel();
    _positionSubscription.cancel();
    _volumeSubscription.cancel();

    // Khôi phục orientation và system UI
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
    super.dispose();
  }

  void _hideControlsAfterDelay() {
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted && _showControls) {
        setState(() {
          _showControls = false;
        });
      }
    });
  }

  void _toggleControls() {
    setState(() {
      _showControls = !_showControls;
    });

    if (_showControls) {
      _hideControlsAfterDelay();
    }
  }

  void _togglePlayPause() {
    if (_isPlaying) {
      widget.player.pause();
    } else {
      widget.player.play();
    }
    _hideControlsAfterDelay();
  }

  void _exitFullscreen() {
    Get.back();
  }

  void _seekTo(Duration position) {
    print('FullscreenPlayer: Seeking to position: $position');
    widget.player.seek(position);
  }

  void _setVolume(double volume) {
    setState(() {
      _volume = volume;
    });
    widget.player.setVolume(volume * 100); // Media kit volume is 0-100
  }

  void _setPlaybackSpeed(double speed) {
    setState(() {
      _playbackSpeed = speed;
    });
    widget.player.setRate(speed);
  }

  void _onSeekStart() {
    print('FullscreenPlayer: Seek start - Current position: $_position');
    setState(() {
      _isDragging = true;
    });
  }

  void _onSeekEnd() {
    print('FullscreenPlayer: Seek end - Final position: $_position');
    setState(() {
      _isDragging = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: GestureDetector(
        onTap: _toggleControls,
        child: Stack(
          children: [
            // Video player
            Center(
              child: Video(
                controller: widget.videoController,
                controls: NoVideoControls,
              ),
            ),

            // Custom controls overlay
            if (_showControls)
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.black.withOpacity(0.7),
                      Colors.transparent,
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                  ),
                ),
                child: Column(
                  children: [
                    // Top controls
                    SafeArea(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Row(
                          children: [
                            IconButton(
                              onPressed: _exitFullscreen,
                              icon: const Icon(
                                Icons.arrow_back,
                                color: Colors.white,
                                size: 28,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Text(
                                widget.movieTitle,
                                style: GoogleFonts.mulish(
                                  color: Colors.white,
                                  fontSize: 18,
                                  fontWeight: FontWeight.w600,
                                ),
                                maxLines: 1,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            // Playback speed button
                            _buildSpeedButton(),
                            // Volume button
                            _buildVolumeButton(),
                          ],
                        ),
                      ),
                    ),

                    const Spacer(),

                    // Center play/pause button
                    Center(
                      child: IconButton(
                        onPressed: _togglePlayPause,
                        icon: Icon(
                          _isPlaying
                              ? Icons.pause_circle_filled
                              : Icons.play_circle_filled,
                          size: 80,
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ),

                    const Spacer(),

                    // Bottom controls - Progress bar
                    SafeArea(
                      child: _buildProgressBar(),
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpeedButton() {
    return PopupMenuButton<double>(
      icon: const Icon(Icons.speed, color: Colors.white, size: 28),
      onSelected: _setPlaybackSpeed,
      itemBuilder: (context) => [
        const PopupMenuItem(value: 0.5, child: Text('0.5x')),
        const PopupMenuItem(value: 0.75, child: Text('0.75x')),
        const PopupMenuItem(value: 1.0, child: Text('1.0x')),
        const PopupMenuItem(value: 1.25, child: Text('1.25x')),
        const PopupMenuItem(value: 1.5, child: Text('1.5x')),
        const PopupMenuItem(value: 2.0, child: Text('2.0x')),
      ],
    );
  }

  Widget _buildVolumeButton() {
    return PopupMenuButton<void>(
      icon: Icon(
        _volume == 0 ? Icons.volume_off : Icons.volume_up,
        color: Colors.white,
        size: 28,
      ),
      itemBuilder: (context) => [
        PopupMenuItem(
          enabled: false,
          child: StatefulBuilder(
            builder: (context, setState) => Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text('Âm lượng: ${(_volume * 100).round()}%'),
                Slider(
                  value: _volume,
                  onChanged: (value) {
                    setState(() {});
                    _setVolume(value);
                  },
                  min: 0.0,
                  max: 1.0,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProgressBar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
      child: Column(
        children: [
          // Progress slider
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Colors.amber,
              inactiveTrackColor: Colors.white.withOpacity(0.3),
              thumbColor: Colors.amber,
              overlayColor: Colors.amber.withOpacity(0.2),
              thumbShape: const RoundSliderThumbShape(enabledThumbRadius: 10),
              trackHeight: 6,
            ),
            child: Slider(
              value: _duration.inMilliseconds > 0
                  ? _position.inMilliseconds / _duration.inMilliseconds
                  : 0.0,
              onChangeStart: (_) => _onSeekStart(),
              onChanged: (value) {
                final newPosition = Duration(
                  milliseconds: (value * _duration.inMilliseconds).round(),
                );
                setState(() {
                  _position = newPosition;
                });
              },
              onChangeEnd: (value) {
                final newPosition = Duration(
                  milliseconds: (value * _duration.inMilliseconds).round(),
                );
                _seekTo(newPosition);
                _onSeekEnd();
              },
            ),
          ),
          // Time labels
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 12),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  _formatDuration(_position),
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                ),
                Text(
                  _formatDuration(_duration),
                  style: const TextStyle(color: Colors.white, fontSize: 16),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }
}
