/// Controller đặt vé để quản lý quy trình đặt vé xem phim
import 'package:get/get.dart';
import '../models/movie_model.dart';
import '../models/theater_model.dart';
import '../models/showtime_model.dart';
import '../models/screen_model.dart';
import '../models/ticket_model.dart';
import '../services/theater_service.dart';
import '../services/showtime_service.dart';
import '../services/screen_service.dart';

class BookingController extends GetxController {
  // Các instance service để quản lý dữ liệu đặt vé
  final TheaterService _theaterService = TheaterService();
  final ShowtimeService _showtimeService = ShowtimeService();
  final ScreenService _screenService = ScreenService();

  // Danh sách dữ liệu observable
  final RxList<TheaterModel> theaters = <TheaterModel>[].obs; // Rạp có sẵn
  final RxList<ShowtimeModel> showtimes =
      <ShowtimeModel>[].obs; // Suất chiếu có sẵn
  final Rx<ScreenModel?> selectedScreen =
      Rx<ScreenModel?>(null); // Chi tiết phòng chiếu đã chọn
  final RxList<String> selectedSeats = <String>[].obs; // ID ghế đã chọn
  final RxDouble totalPrice = 0.0.obs; // Tổng giá đặt vé
  final RxBool isLoading = false.obs; // Trạng thái loading
  final RxString errorMessage = ''.obs; // Thông báo lỗi

  // Lựa chọn của người dùng trong quy trình đặt vé
  final Rx<Movie?> selectedMovie = Rx<Movie?>(null); // Phim đã chọn
  final Rx<TheaterModel?> selectedTheater =
      Rx<TheaterModel?>(null); // Rạp đã chọn
  final Rx<ShowtimeModel?> selectedShowtime =
      Rx<ShowtimeModel?>(null); // Suất chiếu đã chọn

  @override
  void onInit() {
    super.onInit();
    loadTheaters(); // Tải danh sách rạp khi khởi tạo
  }

  /// Tải tất cả rạp chiếu có sẵn
  Future<void> loadTheaters({bool activeOnly = true}) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final theaterList =
          await _theaterService.getAllTheaters(activeOnly: activeOnly);
      theaters.value = theaterList;
    } catch (e) {
      errorMessage.value = 'Không thể tải danh sách rạp: $e';
    } finally {
      isLoading.value = false;
    }
  }

  /// Tải lịch chiếu cho phim và rạp đã chọn với bộ lọc ngày tùy chọn
  Future<void> loadShowtimes(int movieId, String theaterId,
      {String? date}) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      print(
          'BookingController.loadShowtimes: movieId=$movieId, theaterId=$theaterId, date=$date');

      List<ShowtimeModel> showtimeList;
      if (date != null && date.isNotEmpty) {
        // Tải lịch chiếu cho ngày cụ thể
        print('Loading showtimes with date filter');
        showtimeList = await _showtimeService.getShowtimesByMovieTheaterAndDate(
            movieId, theaterId, date);
      } else {
        // Tải tất cả lịch chiếu cho phim và rạp
        print('Loading showtimes without date filter');
        showtimeList = await _showtimeService.getShowtimesByMovieAndTheater(
            movieId, theaterId);
      }

      print('Found ${showtimeList.length} showtimes');
      showtimes.value = showtimeList;
    } catch (e) {
      print('Error loading showtimes: $e');
      errorMessage.value = 'Không thể tải lịch chiếu: $e';
      showtimes.clear();
    } finally {
      isLoading.value = false;
    }
  }

  /// Tải thông tin chi tiết cho phòng chiếu cụ thể
  Future<void> loadScreen(String screenId) async {
    try {
      isLoading.value = true;
      errorMessage.value = '';

      final screen = await _screenService.getScreenById(screenId);
      selectedScreen.value = screen;
    } catch (e) {
      errorMessage.value = 'Không thể tải thông tin phòng chiếu: $e';
      selectedScreen.value = null;
    } finally {
      isLoading.value = false;
    }
  }

  // ===== CÁC PHƯƠNG THỨC CHỌN LỰA =====

  /// Đặt phim đã chọn và reset các lựa chọn phụ thuộc
  void setSelectedMovie(Movie movie) {
    selectedMovie.value = movie;
    // Clear all dependent selections to start fresh
    // Xóa tất cả lựa chọn phụ thuộc để bắt đầu mới
    selectedTheater.value = null;
    selectedShowtime.value = null;
    selectedScreen.value = null;
    selectedSeats.clear();
    showtimes.clear();
    totalPrice.value = 0.0;
  }

  /// Set selected theater and load showtimes
  /// Đặt rạp đã chọn và tải lịch chiếu
  void setSelectedTheater(TheaterModel theater, {String? selectedDate}) {
    selectedTheater.value = theater;
    // Clear dependent selections
    // Xóa các lựa chọn phụ thuộc
    selectedShowtime.value = null;
    selectedScreen.value = null;
    selectedSeats.clear();
    showtimes.clear();
    totalPrice.value = 0.0;
    errorMessage.value = ''; // Clear previous errors / Xóa lỗi trước đó

    print('setSelectedTheater: ${theater.name}, selectedDate: $selectedDate');

    // Load showtimes for this theater and movie combination
    // Tải lịch chiếu cho sự kết hợp rạp và phim này
    if (selectedMovie.value != null) {
      loadShowtimes(selectedMovie.value!.id, theater.id, date: selectedDate);
    }
  }

  /// Set selected showtime and load screen details
  /// Đặt suất chiếu đã chọn và tải chi tiết phòng chiếu
  void setSelectedShowtime(ShowtimeModel showtime) {
    selectedShowtime.value = showtime;
    // Clear seat selections for new showtime
    // Xóa lựa chọn ghế cho suất chiếu mới
    selectedSeats.clear();
    totalPrice.value = 0.0;

    // Load screen layout and details
    // Tải bố cục và chi tiết phòng chiếu
    loadScreen(showtime.screenId);
  }

  // ===== SEAT MANAGEMENT =====
  // ===== QUẢN LÝ GHẾ =====

  /// Toggle seat selection (select/deselect)
  /// Chuyển đổi lựa chọn ghế (chọn/bỏ chọn)
  void toggleSeat(String seatId) {
    if (selectedSeats.contains(seatId)) {
      selectedSeats.remove(seatId);
    } else {
      selectedSeats.add(seatId);
    }
    calculateTotalPrice(); // Recalculate price after seat change / Tính lại giá sau khi thay đổi ghế
  }

  /// Calculate total price based on selected seats and their types
  /// Tính tổng giá dựa trên ghế đã chọn và loại ghế
  void calculateTotalPrice() {
    if (selectedShowtime.value == null || selectedSeats.isEmpty) {
      totalPrice.value = 0.0;
      return;
    }

    double total = 0.0;
    final showtime = selectedShowtime.value!;

    for (String seatId in selectedSeats) {
      // Determine seat type based on seat ID prefix
      // Xác định loại ghế dựa trên tiền tố ID ghế
      String seatType = 'standard';
      if (seatId.startsWith('V')) {
        seatType = 'vip';
      } else if (seatId.startsWith('P')) {
        seatType = 'premium';
      }

      total += showtime.pricing.getPriceForSeatType(seatType);
    }

    totalPrice.value = total;
  }

  /// Check if a seat is available for booking
  /// Kiểm tra xem ghế có sẵn để đặt không
  bool isSeatAvailable(String seatId) {
    if (selectedShowtime.value == null) return false;

    final showtime = selectedShowtime.value!;
    return !showtime.bookedSeats.contains(seatId) &&
        !showtime.reservedSeats.contains(seatId);
  }

  /// Check if a seat is currently selected by user
  /// Kiểm tra xem ghế có đang được người dùng chọn không
  bool isSeatSelected(String seatId) {
    return selectedSeats.contains(seatId);
  }

  // ===== BOOKING SUMMARY AND VALIDATION =====
  // ===== TÓM TẮT VÀ XÁC THỰC ĐẶT VÉ =====

  /// Get complete booking summary for confirmation
  /// Lấy tóm tắt đặt vé đầy đủ để xác nhận
  Map<String, dynamic> getBookingSummary() {
    return {
      'movie': selectedMovie.value,
      'theater': selectedTheater.value,
      'showtime': selectedShowtime.value,
      'screen': selectedScreen.value,
      'seats': selectedSeats.toList(),
      'totalPrice': totalPrice.value,
      'seatCount': selectedSeats.length,
    };
  }

  /// Validate if booking has all required information
  /// Xác thực xem đặt vé có đủ thông tin bắt buộc không
  bool isBookingValid() {
    return selectedMovie.value != null &&
        selectedTheater.value != null &&
        selectedShowtime.value != null &&
        selectedScreen.value != null &&
        selectedSeats.isNotEmpty;
  }

  /// Clear all booking selections and reset state
  /// Xóa tất cả lựa chọn đặt vé và reset trạng thái
  void clearBooking() {
    selectedMovie.value = null;
    selectedTheater.value = null;
    selectedShowtime.value = null;
    selectedScreen.value = null;
    selectedSeats.clear();
    showtimes.clear();
    totalPrice.value = 0.0;
    errorMessage.value = '';
  }

  // ===== UTILITY METHODS =====
  // ===== CÁC PHƯƠNG THỨC TIỆN ÍCH =====

  /// Get list of available dates for current movie and theater
  /// Lấy danh sách ngày có sẵn cho phim và rạp hiện tại
  List<String> getAvailableDates() {
    if (showtimes.isEmpty) return [];

    final dates = showtimes.map((showtime) => showtime.date).toSet().toList();
    dates.sort();
    return dates;
  }

  /// Get showtimes for a specific date
  /// Lấy lịch chiếu cho ngày cụ thể
  List<ShowtimeModel> getShowtimesForDate(String date) {
    return showtimes.where((showtime) => showtime.date == date).toList();
  }

  /// Format price with Vietnamese currency
  /// Định dạng giá với đơn vị tiền tệ Việt Nam
  String formatPrice(double price) {
    return '${price.toStringAsFixed(0).replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]},',
        )} VNĐ';
  }

  /// Get localized display name for seat type
  /// Lấy tên hiển thị bản địa hóa cho loại ghế
  String getSeatTypeDisplayName(String seatType) {
    switch (seatType) {
      case 'vip':
        return 'VIP';
      case 'premium':
        return 'Premium';
      case 'standard':
        return 'Thường';
      default:
        return 'Thường';
    }
  }
}
