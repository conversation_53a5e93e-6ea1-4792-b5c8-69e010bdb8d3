# 2. PHÂN TÍCH YÊU CẦU

## 2.1 Tổng quan về yêu cầu

D<PERSON> "Đớ<PERSON> Phim" đ<PERSON><PERSON><PERSON> phát triển dựa trên việc phân tích chi tiết các yêu cầu từ người dùng cuối, rạp chiếu phim và các bên liên quan khác. <PERSON>ệ thống được thiết kế để đáp ứng nhu cầu đặt vé xem phim trực tuyến với trải nghiệm người dùng tối ưu và quản lý hiệu quả cho các rạp chiếu.

### 2.1.1 Phương pháp thu thập yêu cầu
- **Khảo sát người dùng:** Phỏng vấn 50+ người dùng về thói quen xem phim
- **<PERSON>ân tích đối thủ:** <PERSON><PERSON><PERSON><PERSON> cứu các ứng dụng đặt vé hiện có (CGV, Galaxy, Lotte)
- **Tham khảo chuyên gia:** Trao đổi với quản lý rạp chiếu về quy trình vận hành
- **Nghiên cứu thị trường:** Phân tích xu hướng công nghệ và hành vi người tiêu dùng

### 2.1.2 Phân loại yêu cầu
- **Yêu cầu chức năng (Functional Requirements):** Các tính năng cụ thể mà hệ thống phải có
- **Yêu cầu phi chức năng (Non-functional Requirements):** Hiệu suất, bảo mật, khả năng mở rộng
- **Yêu cầu ràng buộc (Constraints):** Giới hạn về công nghệ, thời gian, ngân sách
- **Yêu cầu giao diện (Interface Requirements):** Tương tác với hệ thống bên ngoài

## 2.2 Yêu cầu chức năng chi tiết

### 2.2.1 Module Xác thực và Quản lý Người dùng

#### FR-001: Đăng ký tài khoản
**Mô tả:** Người dùng có thể tạo tài khoản mới để sử dụng ứng dụng
**Đầu vào:** Email, mật khẩu, tên hiển thị
**Đầu ra:** Tài khoản được tạo thành công, email xác thực được gửi
**Quy tắc nghiệp vụ:**
- Email phải hợp lệ và chưa được sử dụng
- Mật khẩu tối thiểu 6 ký tự
- Tên hiển thị không được để trống
- Tự động tạo avatar mặc định từ tên

#### FR-002: Đăng nhập
**Mô tả:** Người dùng đăng nhập vào hệ thống
**Đầu vào:** Email/số điện thoại và mật khẩu
**Đầu ra:** Phiên đăng nhập được tạo, chuyển đến trang chủ
**Quy tắc nghiệp vụ:**
- Hỗ trợ đăng nhập bằng Google
- Tự động đăng nhập lần sau (Remember me)
- Khóa tài khoản sau 5 lần đăng nhập sai

#### FR-003: Quản lý hồ sơ
**Mô tả:** Người dùng có thể xem và chỉnh sửa thông tin cá nhân
**Chức năng:**
- Thay đổi tên hiển thị
- Cập nhật ảnh đại diện (upload lên Firebase Storage)
- Thay đổi mật khẩu
- Xem lịch sử giao dịch

#### FR-004: Phân quyền người dùng
**Mô tả:** Hệ thống hỗ trợ 3 cấp độ người dùng
**Các vai trò:**
- **User:** Đặt vé, xem phim, quản lý vé cá nhân
- **Admin:** Quản lý rạp, phim, lịch chiếu, người dùng
- **Developer:** Tất cả quyền admin + debug tools

### 2.2.2 Module Khám phá và Tìm kiếm Phim

#### FR-005: Duyệt danh sách phim
**Mô tả:** Hiển thị danh sách phim theo các danh mục
**Chức năng:**
- Phim đang chiếu (Now Playing)
- Phim sắp chiếu (Coming Soon)
- Phim theo thể loại (Action, Comedy, Drama, etc.)
- Phim yêu thích cá nhân

#### FR-006: Tìm kiếm phim
**Mô tả:** Người dùng có thể tìm kiếm phim theo nhiều tiêu chí
**Tiêu chí tìm kiếm:**
- Tên phim (tiếng Việt và tiếng Anh)
- Thể loại
- Diễn viên
- Đạo diễn
- Năm sản xuất

#### FR-007: Xem chi tiết phim
**Mô tả:** Hiển thị thông tin đầy đủ về phim
**Thông tin bao gồm:**
- Poster và banner phim
- Tóm tắt nội dung
- Thời lượng, thể loại, độ tuổi
- Diễn viên, đạo diễn
- Trailer video (tích hợp Media Kit)
- Đánh giá từ người dùng
- Lịch chiếu tại các rạp

#### FR-008: Phát trailer
**Mô tả:** Người dùng có thể xem trailer phim trực tiếp trong ứng dụng
**Tính năng:**
- Phát video chất lượng cao
- Điều khiển phát/dừng, tua
- Chế độ toàn màn hình
- Tự động dừng khi thoát

### 2.2.3 Module Đặt vé và Chọn ghế

#### FR-009: Quy trình đặt vé 5 bước
**Mô tả:** Quy trình đặt vé được chia thành 5 bước rõ ràng

**Bước 1: Chọn phim**
- Hiển thị danh sách phim đang chiếu
- Xem chi tiết phim và trailer
- Chọn phim muốn xem

**Bước 2: Chọn rạp chiếu**
- Hiển thị danh sách rạp có chiếu phim
- Thông tin địa chỉ, khoảng cách
- Lọc theo khu vực

**Bước 3: Chọn suất chiếu**
- Hiển thị lịch chiếu theo ngày
- Thông tin giờ chiếu, phòng chiếu
- Giá vé cho từng suất

**Bước 4: Chọn ghế ngồi**
- Sơ đồ ghế thời gian thực
- Phân biệt ghế trống/đã đặt/đang chọn
- Tính năng chọn ghế liền kề tự động

**Bước 5: Thanh toán**
- Xác nhận thông tin đặt vé
- Chọn phương thức thanh toán
- Xử lý thanh toán an toàn

#### FR-010: Hệ thống chọn ghế thời gian thực
**Mô tả:** Quản lý trạng thái ghế ngồi theo thời gian thực
**Trạng thái ghế:**
- **Trống:** Có thể chọn
- **Đang chọn:** Người khác đang chọn (màu vàng)
- **Đã đặt:** Không thể chọn (màu đỏ)
- **Đang giữ:** Người dùng hiện tại đang chọn (màu xanh)

**Quy tắc nghiệp vụ:**
- Giữ ghế tối đa 10 phút
- Tự động hủy nếu không thanh toán
- Cập nhật trạng thái real-time cho tất cả users
- Ngăn chặn đặt trùng ghế

#### FR-011: Tính toán giá vé
**Mô tả:** Tự động tính toán tổng tiền dựa trên ghế đã chọn
**Yếu tố ảnh hưởng giá:**
- Loại ghế (thường, VIP, couple)
- Suất chiếu (sáng, chiều, tối, đêm)
- Ngày trong tuần (thường, cuối tuần)
- Khuyến mãi đặc biệt

### 2.2.4 Module Thanh toán

#### FR-012: Tích hợp PayPal
**Mô tả:** Xử lý thanh toán qua PayPal
**Tính năng:**
- Chuyển đổi VND sang USD tự động
- Sandbox mode cho testing
- Production mode cho thực tế
- Xử lý callback thành công/thất bại

#### FR-013: Quản lý giao dịch
**Mô tả:** Theo dõi và quản lý các giao dịch thanh toán
**Trạng thái giao dịch:**
- Pending: Đang xử lý
- Completed: Thành công
- Failed: Thất bại
- Cancelled: Đã hủy
- Refunded: Đã hoàn tiền

#### FR-014: Xử lý lỗi thanh toán
**Mô tả:** Xử lý các trường hợp thanh toán không thành công
**Quy trình:**
- Giữ ghế trong 15 phút sau lỗi
- Cho phép thử lại thanh toán
- Tự động hủy vé nếu không thành công
- Thông báo lỗi chi tiết cho người dùng

### 2.2.5 Module Quản lý Vé

#### FR-015: Vé điện tử
**Mô tả:** Tạo và quản lý vé điện tử
**Thông tin vé:**
- Mã đặt vé duy nhất
- QR Code để quét tại rạp
- Thông tin phim, rạp, suất chiếu
- Ghế ngồi đã đặt
- Thời gian đặt vé

#### FR-016: Quản lý danh sách vé
**Mô tả:** Người dùng có thể xem và quản lý vé đã đặt
**Phân loại:**
- Vé sắp tới (upcoming)
- Vé đã qua (past)
- Vé đã hủy (cancelled)

#### FR-017: Hủy vé
**Mô tả:** Cho phép hủy vé trong điều kiện nhất định
**Quy tắc:**
- Chỉ hủy được trước 2 giờ chiếu
- Hoàn tiền 80% giá vé
- Tự động cập nhật trạng thái ghế
- Gửi thông báo xác nhận

### 2.2.6 Module Thông báo

#### FR-018: Thông báo thời gian thực
**Mô tả:** Hệ thống thông báo sử dụng Firebase Realtime Database
**Loại thông báo:**
- Xác nhận đặt vé thành công
- Nhắc nhở trước giờ chiếu
- Khuyến mãi và ưu đãi đặc biệt
- Phim mới ra mắt
- Thay đổi lịch chiếu

#### FR-019: Quản lý thông báo
**Mô tả:** Người dùng có thể quản lý thông báo
**Tính năng:**
- Đánh dấu đã đọc/chưa đọc
- Xóa thông báo
- Đánh dấu tất cả đã đọc
- Cài đặt loại thông báo muốn nhận

### 2.2.7 Module Admin

#### FR-020: Quản lý rạp chiếu
**Mô tả:** Admin có thể quản lý thông tin rạp chiếu
**Chức năng:**
- Thêm/sửa/xóa rạp chiếu
- Quản lý thông tin địa chỉ, liên hệ
- Cập nhật trạng thái hoạt động
- Import hàng loạt từ Excel/CSV

#### FR-021: Quản lý phòng chiếu
**Mô tả:** Quản lý các phòng chiếu trong rạp
**Chức năng:**
- Tạo sơ đồ ghế ngồi
- Cấu hình loại ghế (thường, VIP, couple)
- Thiết lập sức chứa phòng
- Khóa/mở phòng chiếu

#### FR-022: Quản lý lịch chiếu
**Mô tả:** Lập và quản lý lịch chiếu phim
**Chức năng:**
- Tạo suất chiếu mới
- Gán phim vào phòng chiếu
- Thiết lập giá vé theo suất
- Xử lý xung đột lịch chiếu

#### FR-023: Báo cáo và thống kê
**Mô tả:** Cung cấp báo cáo chi tiết cho admin
**Loại báo cáo:**
- Doanh thu theo ngày/tháng/năm
- Số lượng vé bán theo phim
- Tỷ lệ lấp đầy phòng chiếu
- Thống kê người dùng hoạt động

## 2.3 Yêu cầu phi chức năng

### 2.3.1 Hiệu suất (Performance)

#### NFR-001: Thời gian phản hồi
- Tải trang chủ: < 2 giây
- Tìm kiếm phim: < 1 giây
- Cập nhật trạng thái ghế: < 500ms
- Xử lý thanh toán: < 10 giây

#### NFR-002: Khả năng chịu tải
- Hỗ trợ 1000 người dùng đồng thời
- Xử lý 100 giao dịch/phút
- Uptime 99.5%
- Tự động scale khi cần thiết

#### NFR-003: Tối ưu hóa
- Kích thước ứng dụng < 50MB
- Sử dụng RAM < 200MB
- Tối ưu hình ảnh và video
- Lazy loading cho danh sách dài

### 2.3.2 Bảo mật (Security)

#### NFR-004: Xác thực và phân quyền
- Firebase Authentication với Google Sign-In
- JWT token với thời gian hết hạn
- Role-based access control
- Two-factor authentication (tùy chọn)

#### NFR-005: Bảo vệ dữ liệu
- Mã hóa dữ liệu nhạy cảm
- HTTPS cho tất cả API calls
- Firestore Security Rules
- Input validation và sanitization

#### NFR-006: Thanh toán an toàn
- PCI DSS compliance qua PayPal
- Không lưu trữ thông tin thẻ
- Mã hóa thông tin giao dịch
- Audit trail cho tất cả giao dịch

### 2.3.3 Khả năng sử dụng (Usability)

#### NFR-007: Giao diện người dùng
- Material Design guidelines
- Responsive design cho mọi kích thước màn hình
- Dark/Light theme
- Accessibility support (WCAG 2.1)

#### NFR-008: Đa ngôn ngữ
- Hỗ trợ tiếng Việt và tiếng Anh
- Chuyển đổi ngôn ngữ real-time
- Localization cho định dạng ngày, tiền tệ
- RTL support (chuẩn bị cho tương lai)

#### NFR-009: Trải nghiệm người dùng
- Onboarding flow cho người dùng mới
- Offline mode cho một số tính năng
- Push notifications
- Deep linking support

### 2.3.4 Khả năng bảo trì (Maintainability)

#### NFR-010: Kiến trúc code
- Clean Architecture với MVVM pattern
- Dependency injection với GetX
- Unit test coverage > 80%
- Code documentation đầy đủ

#### NFR-011: Monitoring và logging
- Firebase Analytics integration
- Crashlytics cho error tracking
- Performance monitoring
- Custom events tracking

#### NFR-012: CI/CD
- Automated testing pipeline
- Automated deployment
- Code quality checks
- Security vulnerability scanning

### 2.3.5 Khả năng mở rộng (Scalability)

#### NFR-013: Kiến trúc hệ thống
- Microservices với Firebase Functions
- Horizontal scaling capability
- Load balancing
- Database sharding (khi cần)

#### NFR-014: Tích hợp bên ngoài
- RESTful API design
- GraphQL support (tương lai)
- Webhook support
- Third-party integrations

## 2.4 Yêu cầu ràng buộc

### 2.4.1 Công nghệ
- **Frontend:** Flutter 3.x
- **Backend:** Firebase (Firestore, Functions, Auth)
- **Payment:** PayPal SDK
- **Video:** Media Kit
- **State Management:** GetX

### 2.4.2 Nền tảng
- **Mobile:** Android 7.0+, iOS 11.0+
- **Web:** Chrome, Firefox, Safari, Edge
- **Responsive:** Tablet và desktop support

### 2.4.3 Tuân thủ
- GDPR compliance cho dữ liệu người dùng
- Luật bảo vệ người tiêu dùng Việt Nam
- App Store và Google Play policies
- Accessibility standards

## 2.5 Use Cases chính

### 2.5.1 Use Case: Đặt vé xem phim

**Actor:** Người dùng đã đăng nhập
**Mục tiêu:** Đặt vé xem phim thành công
**Điều kiện tiên quyết:** Người dùng đã đăng nhập, có phim đang chiếu

**Luồng chính:**
1. Người dùng chọn phim từ danh sách
2. Hệ thống hiển thị chi tiết phim và danh sách rạp
3. Người dùng chọn rạp chiếu
4. Hệ thống hiển thị lịch chiếu
5. Người dùng chọn suất chiếu
6. Hệ thống hiển thị sơ đồ ghế
7. Người dùng chọn ghế ngồi
8. Hệ thống tính toán tổng tiền
9. Người dùng xác nhận và chọn phương thức thanh toán
10. Hệ thống xử lý thanh toán
11. Hệ thống tạo vé điện tử và gửi thông báo

**Luồng thay thế:**
- 2a. Không có rạp nào chiếu phim → Thông báo lỗi
- 5a. Không có suất chiếu → Gợi ý ngày khác
- 7a. Ghế đã được đặt → Cập nhật sơ đồ, yêu cầu chọn lại
- 10a. Thanh toán thất bại → Giữ ghế 15 phút, cho phép thử lại

### 2.5.2 Use Case: Quản lý lịch chiếu (Admin)

**Actor:** Admin
**Mục tiêu:** Tạo lịch chiếu mới cho phim
**Điều kiện tiên quyết:** Admin đã đăng nhập, có phim và rạp trong hệ thống

**Luồng chính:**
1. Admin truy cập trang quản lý lịch chiếu
2. Admin chọn "Tạo lịch chiếu mới"
3. Hệ thống hiển thị form tạo lịch chiếu
4. Admin chọn phim
5. Admin chọn rạp và phòng chiếu
6. Admin thiết lập thời gian chiếu
7. Admin thiết lập giá vé
8. Hệ thống kiểm tra xung đột lịch chiếu
9. Admin xác nhận tạo lịch chiếu
10. Hệ thống lưu lịch chiếu và thông báo thành công

**Luồng thay thế:**
- 8a. Có xung đột lịch chiếu → Thông báo lỗi, yêu cầu chọn thời gian khác
- 8b. Phòng chiếu không hoạt động → Thông báo lỗi

## 2.6 User Stories

### 2.6.1 Epic: Đặt vé xem phim

**US-001:** Là một người dùng, tôi muốn duyệt danh sách phim đang chiếu để chọn phim muốn xem
- **Acceptance Criteria:**
  - Hiển thị poster, tên phim, thể loại, thời lượng
  - Có thể lọc theo thể loại
  - Có thể sắp xếp theo tên, ngày ra mắt, đánh giá
  - Load nhanh với lazy loading

**US-002:** Là một người dùng, tôi muốn xem chi tiết phim để quyết định có xem hay không
- **Acceptance Criteria:**
  - Hiển thị đầy đủ thông tin phim
  - Có thể xem trailer
  - Xem đánh giá từ người dùng khác
  - Nút "Đặt vé" rõ ràng

**US-003:** Là một người dùng, tôi muốn chọn ghế ngồi trực quan để có vị trí tốt nhất
- **Acceptance Criteria:**
  - Sơ đồ ghế rõ ràng, dễ hiểu
  - Phân biệt được ghế trống/đã đặt/đang chọn
  - Cập nhật real-time khi có người khác đặt
  - Tự động tính tiền khi chọn ghế

### 2.6.2 Epic: Quản lý tài khoản

**US-004:** Là một người dùng mới, tôi muốn đăng ký tài khoản dễ dàng để sử dụng ứng dụng
- **Acceptance Criteria:**
  - Form đăng ký đơn giản, ít trường bắt buộc
  - Có thể đăng ký bằng Google
  - Xác thực email tự động
  - Tạo avatar mặc định

**US-005:** Là một người dùng, tôi muốn quản lý thông tin cá nhân để cập nhật khi cần
- **Acceptance Criteria:**
  - Có thể thay đổi tên, ảnh đại diện
  - Thay đổi mật khẩu an toàn
  - Xem lịch sử giao dịch
  - Cài đặt thông báo

### 2.6.3 Epic: Thanh toán

**US-006:** Là một người dùng, tôi muốn thanh toán an toàn và nhanh chóng
- **Acceptance Criteria:**
  - Hỗ trợ PayPal
  - Quy trình thanh toán ít bước
  - Thông báo rõ ràng về trạng thái thanh toán
  - Có thể thử lại khi thất bại

### 2.6.4 Epic: Quản trị hệ thống

**US-007:** Là một admin, tôi muốn quản lý rạp chiếu hiệu quả
- **Acceptance Criteria:**
  - Thêm/sửa/xóa rạp dễ dàng
  - Import hàng loạt từ Excel
  - Quản lý trạng thái hoạt động
  - Báo cáo chi tiết

**US-008:** Là một admin, tôi muốn theo dõi doanh thu và hiệu suất
- **Acceptance Criteria:**
  - Dashboard tổng quan
  - Báo cáo theo thời gian
  - Thống kê phim bán chạy
  - Export dữ liệu

## 2.7 Acceptance Criteria tổng quát

### 2.7.1 Tiêu chí chất lượng
- **Hiệu suất:** Tất cả trang phải load trong vòng 3 giây
- **Tương thích:** Hoạt động trên Android 7.0+ và iOS 11.0+
- **Bảo mật:** Tất cả dữ liệu nhạy cảm phải được mã hóa
- **Khả năng sử dụng:** Người dùng mới có thể đặt vé trong vòng 5 phút

### 2.7.2 Tiêu chí kinh doanh
- **Chuyển đổi:** Tỷ lệ chuyển đổi từ xem phim đến đặt vé > 15%
- **Retention:** Người dùng quay lại sau 30 ngày > 40%
- **Satisfaction:** Điểm đánh giá trung bình > 4.0/5.0
- **Support:** Thời gian phản hồi hỗ trợ < 24 giờ

---

*Phần tiếp theo: 3. Nghiên cứu thị trường và đối thủ cạnh tranh*
