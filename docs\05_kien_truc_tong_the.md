# PHẦN II: THIẾT KẾ HỆ THỐNG

## 5. KIẾN TRÚC TỔNG THỂ

### 5.1 Tổng quan kiến trúc hệ thống

Hệ thống "Đớp Phim" đư<PERSON><PERSON> thiết kế theo mô hình kiến trúc phân tầng (Layered Architecture) kết hợp với Clean Architecture principles, đảm bảo tính mở rộng, bảo trì và kiểm thử. Kiến trúc được chia thành 6 tầng ch<PERSON>h, mỗi tầng có trách nhiệm riêng biệt và tương tác thông qua các interface được định nghĩa rõ ràng.

**[CHÈN ẢNH: System Architecture Diagram - Hình 5.1]**
*Sơ đồ kiến trúc tổng thể hệ thống "Đớp Phim" với 6 tầng chính*

#### 5.1.1 Nguyên tắc thiết kế kiến trúc

**Separation of Concerns:**
- Mỗi tầng có trách nhiệm cụ thể và độc lập
- Business logic tách biệt khỏi UI và data access
- External dependencies được isolate ở tầng ngoài cùng

**Dependency Inversion:**
- Các tầng cao không phụ thuộc vào tầng thấp
- Sử dụng interfaces và abstractions
- Dependency injection với GetX framework

**Single Responsibility:**
- Mỗi class/module có một lý do duy nhất để thay đổi
- Clear boundaries giữa các components
- Loose coupling, high cohesion

**Testability:**
- Mỗi tầng có thể test độc lập
- Mock objects cho external dependencies
- Unit tests, integration tests, widget tests

### 5.2 Chi tiết các tầng kiến trúc

#### 5.2.1 Client Layer - Tầng Ứng dụng

**Mô tả:**
Tầng này bao gồm các ứng dụng client chạy trên các nền tảng khác nhau, được build từ cùng một Flutter codebase.

**Components:**
- **Android App:** Native Android application (API 21+)
- **iOS App:** Native iOS application (iOS 11.0+)
- **Web App:** Progressive Web App chạy trên browsers

**Đặc điểm kỹ thuật:**
```yaml
Platforms:
  Android:
    - Min SDK: 21 (Android 5.0)
    - Target SDK: 34 (Android 14)
    - Architecture: ARM64, x86_64
  iOS:
    - Min Version: 11.0
    - Target: iOS 17
    - Architecture: ARM64
  Web:
    - Browsers: Chrome 90+, Safari 14+, Firefox 88+
    - PWA Support: Service Workers, Offline caching
```

**[CHÈN ẢNH: Multi-platform deployment - Hình 5.2]**
*Screenshots của ứng dụng trên Android, iOS và Web*

#### 5.2.2 Presentation Layer - Tầng Giao diện

**UI Components Sublayer:**
- **Flutter Widgets:** Material Design 3 components
- **Custom Widgets:** Reusable UI components cho dự án
- **Screens & Pages:** Các màn hình chính của ứng dụng
- **Responsive Design:** Adaptive layouts cho different screen sizes

**State Management Sublayer:**
- **GetX Controllers:** Quản lý state và business logic
- **GetX Bindings:** Dependency injection và lifecycle management
- **Reactive Variables:** Observable data với automatic UI updates
- **Navigation Management:** Route management và deep linking

**Code Structure:**
```
lib/view/
├── page/                 # Main screens
│   ├── auth/            # Authentication screens
│   ├── home/            # Home and movie browsing
│   ├── booking/         # Ticket booking flow
│   └── profile/         # User profile management
├── widgets/             # Reusable UI components
│   ├── common/          # Common widgets
│   ├── movie/           # Movie-specific widgets
│   └── booking/         # Booking-specific widgets
└── admin/               # Admin panel screens
```

**[CHÈN ẢNH: UI Component hierarchy - Hình 5.3]**
*Sơ đồ phân cấp các UI components và widgets*

#### 5.2.3 Business Logic Layer - Tầng Logic nghiệp vụ

**Controllers Sublayer:**
Quản lý state và orchestrate business operations:

- **AuthController:** User authentication và session management
- **MovieController:** Movie data và search functionality
- **BookingController:** Ticket booking workflow
- **PaymentController:** Payment processing logic
- **AdminController:** Administrative functions

**Services Sublayer:**
Implement business rules và external integrations:

- **AuthService:** Firebase Authentication integration
- **MovieService:** TMDB API và movie data processing
- **BookingService:** Seat reservation và booking logic
- **PaymentService:** PayPal integration và transaction handling
- **NotificationService:** Real-time notifications

**Business Rules Implementation:**
```dart
// Example: Seat reservation business logic
class BookingService {
  static const int RESERVATION_TIMEOUT = 10; // minutes
  
  Future<bool> reserveSeat(String showtimeId, String seatId) async {
    // 1. Check seat availability
    // 2. Create temporary reservation
    // 3. Set expiration timer
    // 4. Update real-time database
    // 5. Notify other users
  }
}
```

**[CHÈN ẢNH: Business Logic Flow - Hình 5.4]**
*Luồng xử lý business logic cho booking workflow*

#### 5.2.4 Data Layer - Tầng Dữ liệu

**Models Sublayer:**
Data structures và business entities:

```dart
// Core data models
class User {
  final String id;
  final String email;
  final String name;
  final UserRole role;
  final DateTime createdAt;
}

class Movie {
  final int id;
  final String title;
  final String overview;
  final List<String> genres;
  final DateTime releaseDate;
  final double rating;
}

class Ticket {
  final String id;
  final String userId;
  final String showtimeId;
  final List<String> selectedSeats;
  final double totalAmount;
  final TicketStatus status;
}
```

**Local Storage Sublayer:**
- **SharedPreferences:** User settings và app preferences
- **Local Database:** Offline caching với Hive/SQLite
- **File Storage:** Temporary files và cached images

**Data Flow:**
1. **Read:** Local cache → Remote database → UI
2. **Write:** UI → Validation → Remote database → Local cache
3. **Sync:** Background sync khi có network connection

#### 5.2.5 Backend Services - Dịch vụ Backend

**Firebase Services:**
- **Firebase Auth:** User authentication và authorization
- **Cloud Firestore:** Primary database cho structured data
- **Realtime Database:** Real-time features (notifications, seat status)
- **Firebase Storage:** File storage cho images và documents
- **Cloud Functions:** Server-side business logic
- **Firebase Analytics:** User behavior tracking

**External APIs:**
- **TMDB API:** Movie database và metadata
- **PayPal API:** Payment processing
- **FCM:** Push notifications
- **Google Maps API:** Theater location services (future)

**[CHÈN ẢNH: Backend Architecture - Hình 5.5]**
*Sơ đồ kiến trúc backend với Firebase services*

#### 5.2.6 Infrastructure Layer - Tầng Hạ tầng

**Hosting & Deployment:**
- **Firebase Hosting:** Web app deployment
- **Google Play Store:** Android app distribution
- **Apple App Store:** iOS app distribution
- **CDN:** Content delivery network cho static assets

**Monitoring & Analytics:**
- **Firebase Crashlytics:** Error tracking và crash reporting
- **Firebase Performance:** Performance monitoring
- **Firebase Analytics:** User engagement tracking
- **Custom Logging:** Application-specific logging

### 5.3 Patterns và Principles

#### 5.3.1 Design Patterns được sử dụng

**Model-View-Controller (MVC) với GetX:**
```dart
// Controller
class MovieController extends GetxController {
  final MovieService _movieService = Get.find();
  var movies = <Movie>[].obs;
  var isLoading = false.obs;
  
  Future<void> loadMovies() async {
    isLoading.value = true;
    try {
      movies.value = await _movieService.getMovies();
    } finally {
      isLoading.value = false;
    }
  }
}

// View
class MovieListPage extends StatelessWidget {
  final MovieController controller = Get.find();
  
  @override
  Widget build(BuildContext context) {
    return Obx(() => controller.isLoading.value
        ? CircularProgressIndicator()
        : ListView.builder(...)
    );
  }
}
```

**Repository Pattern:**
```dart
abstract class MovieRepository {
  Future<List<Movie>> getMovies();
  Future<Movie> getMovieById(int id);
}

class FirebaseMovieRepository implements MovieRepository {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  @override
  Future<List<Movie>> getMovies() async {
    // Implementation
  }
}
```

**Observer Pattern:**
- GetX reactive programming với .obs variables
- Stream-based data flow cho real-time updates
- Event-driven architecture cho notifications

**Factory Pattern:**
- Service factories cho dependency injection
- Widget factories cho reusable components
- Model factories cho data creation

#### 5.3.2 SOLID Principles Implementation

**Single Responsibility Principle:**
- Mỗi controller chỉ quản lý một domain
- Services có single purpose
- Widgets có single UI responsibility

**Open/Closed Principle:**
- Abstract interfaces cho services
- Plugin architecture cho features
- Extensible widget system

**Liskov Substitution Principle:**
- Interface implementations có thể thay thế
- Mock objects cho testing
- Polymorphic service usage

**Interface Segregation Principle:**
- Small, focused interfaces
- Client-specific abstractions
- Minimal dependencies

**Dependency Inversion Principle:**
- High-level modules không depend on low-level
- Abstractions thay vì concrete implementations
- Dependency injection với GetX

### 5.4 Data Flow Architecture

#### 5.4.1 Unidirectional Data Flow

**Read Flow:**
```
UI Request → Controller → Service → Repository → Database
Database → Repository → Service → Controller → UI Update
```

**Write Flow:**
```
User Action → Controller → Validation → Service → Repository → Database
Database → Real-time Update → All Connected Clients
```

**[CHÈN ẢNH: Data Flow Diagram - Hình 5.6]**
*Sơ đồ luồng dữ liệu trong hệ thống*

#### 5.4.2 Real-time Data Synchronization

**Seat Reservation Flow:**
1. User selects seat → Immediate UI feedback
2. Controller validates selection → Business rules check
3. Service creates reservation → Temporary lock (10 minutes)
4. Realtime Database update → Notify all connected users
5. Other users see seat as "reserved" → UI updates automatically

**Notification Flow:**
1. Server event occurs → Cloud Function triggered
2. Function writes to Realtime Database → Structured notification data
3. Client listeners receive update → Real-time stream
4. Controller processes notification → Business logic
5. UI displays notification → User sees immediately

### 5.5 Security Architecture

#### 5.5.1 Authentication & Authorization

**Multi-layer Security:**
```
Client → Firebase Auth → Custom Claims → Firestore Rules → Data
```

**Role-based Access Control:**
```javascript
// Firestore Security Rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             request.auth.token.role == 'admin';
    }
    
    function isDeveloper() {
      return isAuthenticated() && 
             request.auth.token.role == 'developer';
    }
  }
}
```

#### 5.5.2 Data Protection

**Encryption:**
- Data in transit: HTTPS/TLS 1.3
- Data at rest: Firebase encryption
- Sensitive data: Client-side encryption trước khi store

**Input Validation:**
- Client-side validation cho UX
- Server-side validation cho security
- Sanitization cho all user inputs

**[CHÈN ẢNH: Security Layers - Hình 5.7]**
*Các tầng bảo mật trong hệ thống*

### 5.6 Performance Architecture

#### 5.6.1 Optimization Strategies

**Client-side Optimization:**
- Image caching và lazy loading
- Database query optimization
- Memory management
- Background processing

**Server-side Optimization:**
- Database indexing
- Query optimization
- Caching strategies
- Load balancing

#### 5.6.2 Scalability Design

**Horizontal Scaling:**
- Firebase auto-scaling
- CDN distribution
- Load balancing
- Database sharding (future)

**Vertical Scaling:**
- Resource optimization
- Performance monitoring
- Bottleneck identification
- Capacity planning

**[CHÈN ẢNH: Performance Metrics Dashboard - Hình 5.8]**
*Dashboard hiển thị các metrics hiệu suất hệ thống*

---

*Phần tiếp theo: 6. Thiết kế cơ sở dữ liệu*
